{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/ecommerce/E-Commerce-Website/frontend/src/components/ui/Button.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\n\r\ninterface ButtonProps {\r\n  children: React.ReactNode;\r\n  variant?: 'primary' | 'secondary' | 'outline' | 'danger';\r\n  size?: 'sm' | 'md' | 'lg';\r\n  className?: string;\r\n  onClick?: () => void;\r\n  type?: 'button' | 'submit' | 'reset';\r\n  disabled?: boolean;\r\n}\r\n\r\nexport const Button: React.FC<ButtonProps> = ({\r\n  children,\r\n  variant = 'primary',\r\n  size = 'md',\r\n  className = '',\r\n  onClick,\r\n  type = 'button',\r\n  disabled = false,\r\n}) => {\r\n  const baseClasses = 'font-medium rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2';\r\n\r\n  const variantClasses = {\r\n    primary: 'bg-orange-600 hover:bg-orange-700 text-white focus:ring-orange-500',\r\n    secondary: 'bg-gray-600 hover:bg-gray-700 text-white focus:ring-gray-500',\r\n    outline: 'border-2 border-orange-600 text-orange-600 hover:bg-orange-600 hover:text-white focus:ring-orange-500',\r\n    danger: 'bg-red-600 hover:bg-red-700 text-white focus:ring-red-500',\r\n  };\r\n\r\n  const sizeClasses = {\r\n    sm: 'px-3 py-1.5 text-sm',\r\n    md: 'px-4 py-2 text-base',\r\n    lg: 'px-6 py-3 text-lg',\r\n  };\r\n\r\n  const disabledClasses = disabled ? 'opacity-50 cursor-not-allowed' : '';\r\n\r\n  return (\r\n    <button\r\n      type={type}\r\n      onClick={onClick}\r\n      disabled={disabled}\r\n      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${disabledClasses} ${className}`}\r\n    >\r\n      {children}\r\n    </button>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;;AAcO,MAAM,SAAgC,CAAC,EAC5C,QAAQ,EACR,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,OAAO,EACP,OAAO,QAAQ,EACf,WAAW,KAAK,EACjB;IACC,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,SAAS;QACT,WAAW;QACX,SAAS;QACT,QAAQ;IACV;IAEA,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,kBAAkB,WAAW,kCAAkC;IAErE,qBACE,6LAAC;QACC,MAAM;QACN,SAAS;QACT,UAAU;QACV,WAAW,GAAG,YAAY,CAAC,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,gBAAgB,CAAC,EAAE,WAAW;kBAE1G;;;;;;AAGP;KApCa", "debugId": null}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/ecommerce/E-Commerce-Website/frontend/src/data/products.ts"], "sourcesContent": ["import { Product, ProductCategory } from '@/types';\r\n\r\n// Sample Categories\r\nexport const categories: ProductCategory[] = [\r\n  {\r\n    id: '1',\r\n    name: 'Whole Spices',\r\n    slug: 'whole-spices',\r\n    description: 'Fresh whole spices for maximum flavor and aroma',\r\n    image: '🌶️'\r\n  },\r\n  {\r\n    id: '2',\r\n    name: 'Ground Spices',\r\n    slug: 'ground-spices',\r\n    description: 'Finely ground spices ready for cooking',\r\n    image: '🥄'\r\n  },\r\n  {\r\n    id: '3',\r\n    name: 'Spice Blends',\r\n    slug: 'spice-blends',\r\n    description: 'Expertly crafted blends for authentic flavors',\r\n    image: '🌿'\r\n  },\r\n  {\r\n    id: '4',\r\n    name: 'Herbs',\r\n    slug: 'herbs',\r\n    description: 'Fresh and dried herbs for cooking and seasoning',\r\n    image: '🍃'\r\n  },\r\n  {\r\n    id: '5',\r\n    name: 'Seeds',\r\n    slug: 'seeds',\r\n    description: 'Aromatic seeds for tempering and flavoring',\r\n    image: '🌰'\r\n  }\r\n];\r\n\r\n// Sample Products\r\nexport const products: Product[] = [\r\n  {\r\n    id: '1',\r\n    name: 'Organic Turmeric Powder',\r\n    description: 'Premium organic turmeric powder with high curcumin content. Perfect for curries, golden milk, and health supplements.',\r\n    price: 12.99,\r\n    originalPrice: 15.99,\r\n    category: categories[1], // Ground Spices\r\n    images: ['/images/turmeric.jpg'],\r\n    inStock: true,\r\n    stockQuantity: 50,\r\n    weight: '100g',\r\n    origin: 'India',\r\n    tags: ['organic', 'anti-inflammatory', 'golden milk'],\r\n    rating: 4.8,\r\n    reviewCount: 124,\r\n    createdAt: new Date('2024-01-15'),\r\n    updatedAt: new Date('2024-01-15')\r\n  },\r\n  {\r\n    id: '2',\r\n    name: 'Ceylon Cinnamon Sticks',\r\n    description: 'Authentic Ceylon cinnamon sticks with sweet, delicate flavor. Perfect for baking, tea, and desserts.',\r\n    price: 18.99,\r\n    category: categories[0], // Whole Spices\r\n    images: ['/images/cinnamon.jpg'],\r\n    inStock: true,\r\n    stockQuantity: 30,\r\n    weight: '50g',\r\n    origin: 'Sri Lanka',\r\n    tags: ['ceylon', 'sweet', 'baking'],\r\n    rating: 4.9,\r\n    reviewCount: 89,\r\n    createdAt: new Date('2024-01-10'),\r\n    updatedAt: new Date('2024-01-10')\r\n  },\r\n  {\r\n    id: '3',\r\n    name: 'Garam Masala Blend',\r\n    description: 'Traditional Indian spice blend with cardamom, cinnamon, cloves, and more. Essential for authentic Indian cuisine.',\r\n    price: 14.99,\r\n    category: categories[2], // Spice Blends\r\n    images: ['/images/garam-masala.jpg'],\r\n    inStock: true,\r\n    stockQuantity: 40,\r\n    weight: '75g',\r\n    origin: 'India',\r\n    tags: ['indian', 'blend', 'curry'],\r\n    rating: 4.7,\r\n    reviewCount: 156,\r\n    createdAt: new Date('2024-01-12'),\r\n    updatedAt: new Date('2024-01-12')\r\n  },\r\n  {\r\n    id: '4',\r\n    name: 'Black Peppercorns',\r\n    description: 'Premium whole black peppercorns with intense flavor and aroma. Freshly sourced from Malabar coast.',\r\n    price: 16.99,\r\n    category: categories[0], // Whole Spices\r\n    images: ['/images/black-pepper.jpg'],\r\n    inStock: true,\r\n    stockQuantity: 60,\r\n    weight: '100g',\r\n    origin: 'India',\r\n    tags: ['malabar', 'whole', 'premium'],\r\n    rating: 4.6,\r\n    reviewCount: 78,\r\n    createdAt: new Date('2024-01-08'),\r\n    updatedAt: new Date('2024-01-08')\r\n  },\r\n  {\r\n    id: '5',\r\n    name: 'Smoked Paprika',\r\n    description: 'Spanish smoked paprika with rich, smoky flavor. Perfect for paella, grilled meats, and vegetables.',\r\n    price: 13.99,\r\n    category: categories[1], // Ground Spices\r\n    images: ['/images/paprika.jpg'],\r\n    inStock: true,\r\n    stockQuantity: 35,\r\n    weight: '80g',\r\n    origin: 'Spain',\r\n    tags: ['smoked', 'spanish', 'paella'],\r\n    rating: 4.8,\r\n    reviewCount: 92,\r\n    createdAt: new Date('2024-01-14'),\r\n    updatedAt: new Date('2024-01-14')\r\n  },\r\n  {\r\n    id: '6',\r\n    name: 'Cardamom Pods',\r\n    description: 'Green cardamom pods with intense aroma and flavor. Essential for Indian sweets, tea, and rice dishes.',\r\n    price: 24.99,\r\n    originalPrice: 29.99,\r\n    category: categories[0], // Whole Spices\r\n    images: ['/images/cardamom.jpg'],\r\n    inStock: true,\r\n    stockQuantity: 25,\r\n    weight: '50g',\r\n    origin: 'Guatemala',\r\n    tags: ['green', 'aromatic', 'premium'],\r\n    rating: 4.9,\r\n    reviewCount: 67,\r\n    createdAt: new Date('2024-01-11'),\r\n    updatedAt: new Date('2024-01-11')\r\n  },\r\n  {\r\n    id: '7',\r\n    name: 'Cumin Seeds',\r\n    description: 'Whole cumin seeds with earthy, warm flavor. Perfect for tempering, grinding, and Middle Eastern cuisine.',\r\n    price: 9.99,\r\n    category: categories[4], // Seeds\r\n    images: ['/images/cumin.jpg'],\r\n    inStock: true,\r\n    stockQuantity: 80,\r\n    weight: '100g',\r\n    origin: 'India',\r\n    tags: ['earthy', 'tempering', 'middle-eastern'],\r\n    rating: 4.5,\r\n    reviewCount: 134,\r\n    createdAt: new Date('2024-01-09'),\r\n    updatedAt: new Date('2024-01-09')\r\n  },\r\n  {\r\n    id: '8',\r\n    name: 'Dried Oregano',\r\n    description: 'Mediterranean dried oregano with robust flavor. Perfect for pizza, pasta, and Mediterranean dishes.',\r\n    price: 8.99,\r\n    category: categories[3], // Herbs\r\n    images: ['/images/oregano.jpg'],\r\n    inStock: true,\r\n    stockQuantity: 45,\r\n    weight: '30g',\r\n    origin: 'Greece',\r\n    tags: ['mediterranean', 'pizza', 'pasta'],\r\n    rating: 4.4,\r\n    reviewCount: 98,\r\n    createdAt: new Date('2024-01-13'),\r\n    updatedAt: new Date('2024-01-13')\r\n  },\r\n  {\r\n    id: '9',\r\n    name: 'Star Anise',\r\n    description: 'Whole star anise with sweet licorice flavor. Essential for Asian cuisine, mulled wine, and baking.',\r\n    price: 19.99,\r\n    category: categories[0], // Whole Spices\r\n    images: ['/images/star-anise.jpg'],\r\n    inStock: false,\r\n    stockQuantity: 0,\r\n    weight: '40g',\r\n    origin: 'China',\r\n    tags: ['licorice', 'asian', 'baking'],\r\n    rating: 4.7,\r\n    reviewCount: 45,\r\n    createdAt: new Date('2024-01-07'),\r\n    updatedAt: new Date('2024-01-07')\r\n  },\r\n  {\r\n    id: '10',\r\n    name: 'Curry Powder',\r\n    description: 'Mild curry powder blend with turmeric, coriander, and cumin. Perfect for beginners to Indian cooking.',\r\n    price: 11.99,\r\n    category: categories[2], // Spice Blends\r\n    images: ['/images/curry-powder.jpg'],\r\n    inStock: true,\r\n    stockQuantity: 55,\r\n    weight: '100g',\r\n    origin: 'India',\r\n    tags: ['mild', 'beginner', 'indian'],\r\n    rating: 4.3,\r\n    reviewCount: 187,\r\n    createdAt: new Date('2024-01-06'),\r\n    updatedAt: new Date('2024-01-06')\r\n  }\r\n];\r\n"], "names": [], "mappings": ";;;;AAGO,MAAM,aAAgC;IAC3C;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;IACT;CACD;AAGM,MAAM,WAAsB;IACjC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,eAAe;QACf,UAAU,UAAU,CAAC,EAAE;QACvB,QAAQ;YAAC;SAAuB;QAChC,SAAS;QACT,eAAe;QACf,QAAQ;QACR,QAAQ;QACR,MAAM;YAAC;YAAW;YAAqB;SAAc;QACrD,QAAQ;QACR,aAAa;QACb,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,UAAU,UAAU,CAAC,EAAE;QACvB,QAAQ;YAAC;SAAuB;QAChC,SAAS;QACT,eAAe;QACf,QAAQ;QACR,QAAQ;QACR,MAAM;YAAC;YAAU;YAAS;SAAS;QACnC,QAAQ;QACR,aAAa;QACb,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,UAAU,UAAU,CAAC,EAAE;QACvB,QAAQ;YAAC;SAA2B;QACpC,SAAS;QACT,eAAe;QACf,QAAQ;QACR,QAAQ;QACR,MAAM;YAAC;YAAU;YAAS;SAAQ;QAClC,QAAQ;QACR,aAAa;QACb,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,UAAU,UAAU,CAAC,EAAE;QACvB,QAAQ;YAAC;SAA2B;QACpC,SAAS;QACT,eAAe;QACf,QAAQ;QACR,QAAQ;QACR,MAAM;YAAC;YAAW;YAAS;SAAU;QACrC,QAAQ;QACR,aAAa;QACb,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,UAAU,UAAU,CAAC,EAAE;QACvB,QAAQ;YAAC;SAAsB;QAC/B,SAAS;QACT,eAAe;QACf,QAAQ;QACR,QAAQ;QACR,MAAM;YAAC;YAAU;YAAW;SAAS;QACrC,QAAQ;QACR,aAAa;QACb,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,eAAe;QACf,UAAU,UAAU,CAAC,EAAE;QACvB,QAAQ;YAAC;SAAuB;QAChC,SAAS;QACT,eAAe;QACf,QAAQ;QACR,QAAQ;QACR,MAAM;YAAC;YAAS;YAAY;SAAU;QACtC,QAAQ;QACR,aAAa;QACb,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,UAAU,UAAU,CAAC,EAAE;QACvB,QAAQ;YAAC;SAAoB;QAC7B,SAAS;QACT,eAAe;QACf,QAAQ;QACR,QAAQ;QACR,MAAM;YAAC;YAAU;YAAa;SAAiB;QAC/C,QAAQ;QACR,aAAa;QACb,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,UAAU,UAAU,CAAC,EAAE;QACvB,QAAQ;YAAC;SAAsB;QAC/B,SAAS;QACT,eAAe;QACf,QAAQ;QACR,QAAQ;QACR,MAAM;YAAC;YAAiB;YAAS;SAAQ;QACzC,QAAQ;QACR,aAAa;QACb,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,UAAU,UAAU,CAAC,EAAE;QACvB,QAAQ;YAAC;SAAyB;QAClC,SAAS;QACT,eAAe;QACf,QAAQ;QACR,QAAQ;QACR,MAAM;YAAC;YAAY;YAAS;SAAS;QACrC,QAAQ;QACR,aAAa;QACb,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,UAAU,UAAU,CAAC,EAAE;QACvB,QAAQ;YAAC;SAA2B;QACpC,SAAS;QACT,eAAe;QACf,QAAQ;QACR,QAAQ;QACR,MAAM;YAAC;YAAQ;YAAY;SAAS;QACpC,QAAQ;QACR,aAAa;QACb,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;CACD", "debugId": null}}, {"offset": {"line": 335, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/ecommerce/E-Commerce-Website/frontend/src/components/ui/SearchSuggestions.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useRef } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { products } from '@/data/products';\r\n\r\ninterface SearchSuggestionsProps {\r\n  searchQuery: string;\r\n  onSelect: (query: string) => void;\r\n  isVisible: boolean;\r\n  onClose: () => void;\r\n}\r\n\r\nexport const SearchSuggestions: React.FC<SearchSuggestionsProps> = ({\r\n  searchQuery,\r\n  onSelect,\r\n  isVisible,\r\n  onClose,\r\n}) => {\r\n  const router = useRouter();\r\n  const [suggestions, setSuggestions] = useState<string[]>([]);\r\n  const suggestionsRef = useRef<HTMLDivElement>(null);\r\n\r\n  useEffect(() => {\r\n    if (searchQuery.length > 1) {\r\n      const query = searchQuery.toLowerCase();\r\n      const productSuggestions = new Set<string>();\r\n\r\n      products.forEach(product => {\r\n        // Add product names that match\r\n        if (product.name.toLowerCase().includes(query)) {\r\n          productSuggestions.add(product.name);\r\n        }\r\n        \r\n        // Add category names that match\r\n        if (product.category.name.toLowerCase().includes(query)) {\r\n          productSuggestions.add(product.category.name);\r\n        }\r\n        \r\n        // Add tags that match\r\n        product.tags.forEach(tag => {\r\n          if (tag.toLowerCase().includes(query)) {\r\n            productSuggestions.add(tag);\r\n          }\r\n        });\r\n        \r\n        // Add origins that match\r\n        if (product.origin.toLowerCase().includes(query)) {\r\n          productSuggestions.add(product.origin);\r\n        }\r\n      });\r\n\r\n      setSuggestions(Array.from(productSuggestions).slice(0, 6));\r\n    } else {\r\n      setSuggestions([]);\r\n    }\r\n  }, [searchQuery]);\r\n\r\n  useEffect(() => {\r\n    const handleClickOutside = (event: MouseEvent) => {\r\n      if (suggestionsRef.current && !suggestionsRef.current.contains(event.target as Node)) {\r\n        onClose();\r\n      }\r\n    };\r\n\r\n    if (isVisible) {\r\n      document.addEventListener('mousedown', handleClickOutside);\r\n    }\r\n\r\n    return () => {\r\n      document.removeEventListener('mousedown', handleClickOutside);\r\n    };\r\n  }, [isVisible, onClose]);\r\n\r\n  if (!isVisible || suggestions.length === 0) {\r\n    return null;\r\n  }\r\n\r\n  const handleSuggestionClick = (suggestion: string) => {\r\n    onSelect(suggestion);\r\n    router.push(`/products?search=${encodeURIComponent(suggestion)}`);\r\n  };\r\n\r\n  return (\r\n    <div\r\n      ref={suggestionsRef}\r\n      className=\"absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg z-50 mt-1\"\r\n    >\r\n      <div className=\"py-2\">\r\n        {suggestions.map((suggestion, index) => (\r\n          <button\r\n            key={index}\r\n            onClick={() => handleSuggestionClick(suggestion)}\r\n            className=\"w-full px-4 py-2 text-left hover:bg-gray-100 transition-colors flex items-center\"\r\n          >\r\n            <svg className=\"h-4 w-4 text-gray-400 mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\r\n            </svg>\r\n            <span className=\"text-gray-900\">{suggestion}</span>\r\n          </button>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAaO,MAAM,oBAAsD,CAAC,EAClE,WAAW,EACX,QAAQ,EACR,SAAS,EACT,OAAO,EACR;;IACC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC3D,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,YAAY,MAAM,GAAG,GAAG;gBAC1B,MAAM,QAAQ,YAAY,WAAW;gBACrC,MAAM,qBAAqB,IAAI;gBAE/B,0HAAA,CAAA,WAAQ,CAAC,OAAO;mDAAC,CAAA;wBACf,+BAA+B;wBAC/B,IAAI,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ;4BAC9C,mBAAmB,GAAG,CAAC,QAAQ,IAAI;wBACrC;wBAEA,gCAAgC;wBAChC,IAAI,QAAQ,QAAQ,CAAC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ;4BACvD,mBAAmB,GAAG,CAAC,QAAQ,QAAQ,CAAC,IAAI;wBAC9C;wBAEA,sBAAsB;wBACtB,QAAQ,IAAI,CAAC,OAAO;2DAAC,CAAA;gCACnB,IAAI,IAAI,WAAW,GAAG,QAAQ,CAAC,QAAQ;oCACrC,mBAAmB,GAAG,CAAC;gCACzB;4BACF;;wBAEA,yBAAyB;wBACzB,IAAI,QAAQ,MAAM,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ;4BAChD,mBAAmB,GAAG,CAAC,QAAQ,MAAM;wBACvC;oBACF;;gBAEA,eAAe,MAAM,IAAI,CAAC,oBAAoB,KAAK,CAAC,GAAG;YACzD,OAAO;gBACL,eAAe,EAAE;YACnB;QACF;sCAAG;QAAC;KAAY;IAEhB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM;kEAAqB,CAAC;oBAC1B,IAAI,eAAe,OAAO,IAAI,CAAC,eAAe,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;wBACpF;oBACF;gBACF;;YAEA,IAAI,WAAW;gBACb,SAAS,gBAAgB,CAAC,aAAa;YACzC;YAEA;+CAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;sCAAG;QAAC;QAAW;KAAQ;IAEvB,IAAI,CAAC,aAAa,YAAY,MAAM,KAAK,GAAG;QAC1C,OAAO;IACT;IAEA,MAAM,wBAAwB,CAAC;QAC7B,SAAS;QACT,OAAO,IAAI,CAAC,CAAC,iBAAiB,EAAE,mBAAmB,aAAa;IAClE;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAU;kBAEV,cAAA,6LAAC;YAAI,WAAU;sBACZ,YAAY,GAAG,CAAC,CAAC,YAAY,sBAC5B,6LAAC;oBAEC,SAAS,IAAM,sBAAsB;oBACrC,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;4BAA6B,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACpF,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;sCAEvE,6LAAC;4BAAK,WAAU;sCAAiB;;;;;;;mBAP5B;;;;;;;;;;;;;;;AAajB;GA3Fa;;QAMI,qIAAA,CAAA,YAAS;;;KANb", "debugId": null}}, {"offset": {"line": 490, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/ecommerce/E-Commerce-Website/frontend/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport Link from 'next/link';\r\nimport { Button } from '@/components/ui/Button';\r\nimport { SearchSuggestions } from '@/components/ui/SearchSuggestions';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useCart } from '@/contexts/CartContext';\r\nimport { useWishlist } from '@/contexts/WishlistContext';\r\n\r\nexport const Header = () => {\r\n  const { user, logout } = useAuth();\r\n  const { cartCount } = useCart();\r\n  const { wishlistCount } = useWishlist();\r\n  const router = useRouter();\r\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\r\n  const [isSearchOpen, setIsSearchOpen] = useState(false);\r\n  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);\r\n  const [searchQuery, setSearchQuery] = useState('');\r\n  const [showSuggestions, setShowSuggestions] = useState(false);\r\n\r\n  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);\r\n  const toggleSearch = () => setIsSearchOpen(!isSearchOpen);\r\n\r\n  const handleSearch = (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    if (searchQuery.trim()) {\r\n      // Navigate to products page with search query\r\n      router.push(`/products?search=${encodeURIComponent(searchQuery.trim())}`);\r\n      setSearchQuery(''); // Clear search after navigation\r\n      setIsSearchOpen(false); // Close mobile search\r\n    }\r\n  };\r\n\r\n  return (\r\n    <header className=\"bg-white shadow-sm border-b sticky top-0 z-50\">\r\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n        <div className=\"flex justify-between items-center h-16\">\r\n          {/* Logo */}\r\n          <div className=\"flex items-center\">\r\n            <Link href=\"/\" className=\"flex items-center\">\r\n              <h1 className=\"text-2xl font-bold text-orange-600\">🌶️ SpiceHub</h1>\r\n            </Link>\r\n          </div>\r\n\r\n          {/* Desktop Navigation */}\r\n          <nav className=\"hidden md:flex space-x-8\">\r\n            <Link href=\"/\" className=\"text-gray-700 hover:text-orange-600 transition-colors\">\r\n              Home\r\n            </Link>\r\n            <Link href=\"/products\" className=\"text-gray-700 hover:text-orange-600 transition-colors\">\r\n              Products\r\n            </Link>\r\n            <Link href=\"/categories\" className=\"text-gray-700 hover:text-orange-600 transition-colors\">\r\n              Categories\r\n            </Link>\r\n            <Link href=\"/about\" className=\"text-gray-700 hover:text-orange-600 transition-colors\">\r\n              About\r\n            </Link>\r\n            <Link href=\"/contact\" className=\"text-gray-700 hover:text-orange-600 transition-colors\">\r\n              Contact\r\n            </Link>\r\n          </nav>\r\n\r\n          {/* Search Bar (Desktop) */}\r\n          <div className=\"hidden lg:flex flex-1 max-w-lg mx-8\">\r\n            <form onSubmit={handleSearch} className=\"w-full\">\r\n              <div className=\"relative\">\r\n                <input\r\n                  type=\"text\"\r\n                  placeholder=\"Search for spices...\"\r\n                  value={searchQuery}\r\n                  onChange={(e) => {\r\n                    setSearchQuery(e.target.value);\r\n                    setShowSuggestions(e.target.value.length > 1);\r\n                  }}\r\n                  onFocus={() => setShowSuggestions(searchQuery.length > 1)}\r\n                  className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\"\r\n                />\r\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                  <svg className=\"h-5 w-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\r\n                  </svg>\r\n                </div>\r\n                <SearchSuggestions\r\n                  searchQuery={searchQuery}\r\n                  onSelect={(suggestion) => {\r\n                    setSearchQuery(suggestion);\r\n                    setShowSuggestions(false);\r\n                  }}\r\n                  isVisible={showSuggestions}\r\n                  onClose={() => setShowSuggestions(false)}\r\n                />\r\n              </div>\r\n            </form>\r\n          </div>\r\n\r\n          {/* Right Side Icons & Buttons */}\r\n          <div className=\"flex items-center space-x-4\">\r\n            {/* Search Icon (Mobile) */}\r\n            <button\r\n              onClick={toggleSearch}\r\n              className=\"lg:hidden p-2 text-gray-600 hover:text-orange-600\"\r\n            >\r\n              <svg className=\"h-6 w-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\r\n              </svg>\r\n            </button>\r\n\r\n            {/* Cart Icon */}\r\n            <Link href=\"/cart\" className=\"relative p-2 text-gray-600 hover:text-orange-600 transition-colors\">\r\n              <svg className=\"h-6 w-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01\" />\r\n              </svg>\r\n              {/* Cart Badge */}\r\n              {cartCount > 0 && (\r\n                <span className=\"absolute -top-1 -right-1 bg-orange-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center animate-pulse\">\r\n                  {cartCount}\r\n                </span>\r\n              )}\r\n            </Link>\r\n\r\n            {/* Wishlist Icon */}\r\n            <Link href=\"/wishlist\" className=\"relative p-2 text-gray-600 hover:text-orange-600\">\r\n              <svg className=\"h-6 w-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\" />\r\n              </svg>\r\n              {wishlistCount > 0 && (\r\n                <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\">\r\n                  {wishlistCount}\r\n                </span>\r\n              )}\r\n            </Link>\r\n\r\n            {/* Auth Section */}\r\n            {user ? (\r\n              <div className=\"relative\">\r\n                <button\r\n                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}\r\n                  className=\"flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 transition-colors\"\r\n                >\r\n                  <div className=\"h-8 w-8 bg-orange-600 rounded-full flex items-center justify-center\">\r\n                    <span className=\"text-white text-sm font-semibold\">\r\n                      {user.name.charAt(0).toUpperCase()}\r\n                    </span>\r\n                  </div>\r\n                  <span className=\"hidden md:block text-gray-700 font-medium\">{user.name}</span>\r\n                  <svg className=\"h-4 w-4 text-gray-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\r\n                  </svg>\r\n                </button>\r\n\r\n                {/* User Dropdown Menu */}\r\n                {isUserMenuOpen && (\r\n                  <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50\">\r\n                    <div className=\"px-4 py-2 border-b border-gray-200\">\r\n                      <p className=\"text-sm font-medium text-gray-900\">{user.name}</p>\r\n                      <p className=\"text-sm text-gray-500\">{user.email}</p>\r\n                      <span className=\"inline-block mt-1 px-2 py-1 text-xs bg-orange-100 text-orange-800 rounded-full\">\r\n                        {user.role === 'admin' ? 'Admin' : 'Customer'}\r\n                      </span>\r\n                    </div>\r\n                    <Link href=\"/profile\" className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\">\r\n                      My Profile\r\n                    </Link>\r\n                    <Link href=\"/orders\" className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\">\r\n                      My Orders\r\n                    </Link>\r\n                    <Link href=\"/wishlist\" className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\">\r\n                      Wishlist\r\n                    </Link>\r\n                    {user.role === 'admin' && (\r\n                      <Link href=\"/admin/dashboard\" className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\">\r\n                        Admin Dashboard\r\n                      </Link>\r\n                    )}\r\n                    <div className=\"border-t border-gray-200 mt-2 pt-2\">\r\n                      <button\r\n                        onClick={logout}\r\n                        className=\"block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100\"\r\n                      >\r\n                        Sign Out\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            ) : (\r\n              <div className=\"hidden sm:flex items-center space-x-2\">\r\n                <Link href=\"/auth/login\">\r\n                  <Button variant=\"outline\" size=\"sm\">Login</Button>\r\n                </Link>\r\n                <Link href=\"/auth/register\">\r\n                  <Button size=\"sm\">Sign Up</Button>\r\n                </Link>\r\n              </div>\r\n            )}\r\n\r\n            {/* Mobile Menu Button */}\r\n            <button\r\n              onClick={toggleMenu}\r\n              className=\"md:hidden p-2 text-gray-600 hover:text-orange-600\"\r\n            >\r\n              <svg className=\"h-6 w-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                {isMenuOpen ? (\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n                ) : (\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\r\n                )}\r\n              </svg>\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Mobile Search Bar */}\r\n        {isSearchOpen && (\r\n          <div className=\"lg:hidden py-4 border-t\">\r\n            <form onSubmit={handleSearch}>\r\n              <div className=\"relative\">\r\n                <input\r\n                  type=\"text\"\r\n                  placeholder=\"Search for spices...\"\r\n                  value={searchQuery}\r\n                  onChange={(e) => setSearchQuery(e.target.value)}\r\n                  className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\"\r\n                />\r\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                  <svg className=\"h-5 w-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\r\n                  </svg>\r\n                </div>\r\n              </div>\r\n            </form>\r\n          </div>\r\n        )}\r\n\r\n        {/* Mobile Menu */}\r\n        {isMenuOpen && (\r\n          <div className=\"md:hidden py-4 border-t\">\r\n            <nav className=\"flex flex-col space-y-4\">\r\n              <Link href=\"/\" className=\"text-gray-700 hover:text-orange-600 transition-colors\">\r\n                Home\r\n              </Link>\r\n              <Link href=\"/products\" className=\"text-gray-700 hover:text-orange-600 transition-colors\">\r\n                Products\r\n              </Link>\r\n              <Link href=\"/categories\" className=\"text-gray-700 hover:text-orange-600 transition-colors\">\r\n                Categories\r\n              </Link>\r\n              <Link href=\"/about\" className=\"text-gray-700 hover:text-orange-600 transition-colors\">\r\n                About\r\n              </Link>\r\n              <Link href=\"/contact\" className=\"text-gray-700 hover:text-orange-600 transition-colors\">\r\n                Contact\r\n              </Link>\r\n              <div className=\"flex flex-col space-y-2 pt-4 border-t\">\r\n                {user ? (\r\n                  <>\r\n                    <div className=\"px-4 py-2 bg-gray-50 rounded-lg\">\r\n                      <p className=\"text-sm font-medium text-gray-900\">{user.name}</p>\r\n                      <p className=\"text-xs text-gray-500\">{user.email}</p>\r\n                      <span className=\"inline-block mt-1 px-2 py-1 text-xs bg-orange-100 text-orange-800 rounded-full\">\r\n                        {user.role === 'admin' ? 'Admin' : 'Customer'}\r\n                      </span>\r\n                    </div>\r\n                    <Link href=\"/profile\" className=\"text-gray-700 hover:text-orange-600 transition-colors py-2\">\r\n                      My Profile\r\n                    </Link>\r\n                    <Link href=\"/orders\" className=\"text-gray-700 hover:text-orange-600 transition-colors py-2\">\r\n                      My Orders\r\n                    </Link>\r\n                    {user.role === 'admin' && (\r\n                      <Link href=\"/admin/dashboard\" className=\"text-gray-700 hover:text-orange-600 transition-colors py-2\">\r\n                        Admin Dashboard\r\n                      </Link>\r\n                    )}\r\n                    <button\r\n                      onClick={logout}\r\n                      className=\"text-left text-red-600 hover:text-red-700 transition-colors py-2\"\r\n                    >\r\n                      Sign Out\r\n                    </button>\r\n                  </>\r\n                ) : (\r\n                  <>\r\n                    <Link href=\"/auth/login\">\r\n                      <Button variant=\"outline\" size=\"sm\" className=\"w-full\">Login</Button>\r\n                    </Link>\r\n                    <Link href=\"/auth/register\">\r\n                      <Button size=\"sm\" className=\"w-full\">Sign Up</Button>\r\n                    </Link>\r\n                  </>\r\n                )}\r\n              </div>\r\n            </nav>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </header>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAWO,MAAM,SAAS;;IACpB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC5B,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IACpC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,aAAa,IAAM,cAAc,CAAC;IACxC,MAAM,eAAe,IAAM,gBAAgB,CAAC;IAE5C,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,YAAY,IAAI,IAAI;YACtB,8CAA8C;YAC9C,OAAO,IAAI,CAAC,CAAC,iBAAiB,EAAE,mBAAmB,YAAY,IAAI,KAAK;YACxE,eAAe,KAAK,gCAAgC;YACpD,gBAAgB,QAAQ,sBAAsB;QAChD;IACF;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CACvB,cAAA,6LAAC;oCAAG,WAAU;8CAAqC;;;;;;;;;;;;;;;;sCAKvD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAwD;;;;;;8CAGjF,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAY,WAAU;8CAAwD;;;;;;8CAGzF,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAc,WAAU;8CAAwD;;;;;;8CAG3F,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAAwD;;;;;;8CAGtF,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAwD;;;;;;;;;;;;sCAM1F,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,UAAU;gCAAc,WAAU;0CACtC,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC;gDACT,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC7B,mBAAmB,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG;4CAC7C;4CACA,SAAS,IAAM,mBAAmB,YAAY,MAAM,GAAG;4CACvD,WAAU;;;;;;sDAEZ,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;gDAAwB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC/E,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,6LAAC,gJAAA,CAAA,oBAAiB;4CAChB,aAAa;4CACb,UAAU,CAAC;gDACT,eAAe;gDACf,mBAAmB;4CACrB;4CACA,WAAW;4CACX,SAAS,IAAM,mBAAmB;;;;;;;;;;;;;;;;;;;;;;sCAO1C,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAKzE,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAQ,WAAU;;sDAC3B,6LAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCAGtE,YAAY,mBACX,6LAAC;4CAAK,WAAU;sDACb;;;;;;;;;;;;8CAMP,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAY,WAAU;;sDAC/B,6LAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCAEtE,gBAAgB,mBACf,6LAAC;4CAAK,WAAU;sDACb;;;;;;;;;;;;gCAMN,qBACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS,IAAM,kBAAkB,CAAC;4CAClC,WAAU;;8DAEV,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEACb,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;8DAGpC,6LAAC;oDAAK,WAAU;8DAA6C,KAAK,IAAI;;;;;;8DACtE,6LAAC;oDAAI,WAAU;oDAAwB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC/E,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;wCAKxE,gCACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAqC,KAAK,IAAI;;;;;;sEAC3D,6LAAC;4DAAE,WAAU;sEAAyB,KAAK,KAAK;;;;;;sEAChD,6LAAC;4DAAK,WAAU;sEACb,KAAK,IAAI,KAAK,UAAU,UAAU;;;;;;;;;;;;8DAGvC,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;8DAA0D;;;;;;8DAG1F,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAU,WAAU;8DAA0D;;;;;;8DAGzF,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAY,WAAU;8DAA0D;;;;;;gDAG1F,KAAK,IAAI,KAAK,yBACb,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAmB,WAAU;8DAA0D;;;;;;8DAIpG,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDACC,SAAS;wDACT,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;yDAQT,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;0DAAK;;;;;;;;;;;sDAEtC,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,MAAK;0DAAK;;;;;;;;;;;;;;;;;8CAMxB,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAChE,2BACC,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;iEAErE,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAQ9E,8BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAK,UAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,WAAU;;;;;;8CAEZ,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;wCAAwB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC/E,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAShF,4BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CAAwD;;;;;;0CAGjF,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAY,WAAU;0CAAwD;;;;;;0CAGzF,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAc,WAAU;0CAAwD;;;;;;0CAG3F,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAS,WAAU;0CAAwD;;;;;;0CAGtF,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAW,WAAU;0CAAwD;;;;;;0CAGxF,6LAAC;gCAAI,WAAU;0CACZ,qBACC;;sDACE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAqC,KAAK,IAAI;;;;;;8DAC3D,6LAAC;oDAAE,WAAU;8DAAyB,KAAK,KAAK;;;;;;8DAChD,6LAAC;oDAAK,WAAU;8DACb,KAAK,IAAI,KAAK,UAAU,UAAU;;;;;;;;;;;;sDAGvC,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;sDAA6D;;;;;;sDAG7F,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAU,WAAU;sDAA6D;;;;;;wCAG3F,KAAK,IAAI,KAAK,yBACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAmB,WAAU;sDAA6D;;;;;;sDAIvG,6LAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;;iEAKH;;sDACE,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;gDAAK,WAAU;0DAAS;;;;;;;;;;;sDAEzD,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAK,WAAU;0DAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW3D;GAlSa;;QACc,kIAAA,CAAA,UAAO;QACV,kIAAA,CAAA,UAAO;QACH,sIAAA,CAAA,cAAW;QACtB,qIAAA,CAAA,YAAS;;;KAJb", "debugId": null}}, {"offset": {"line": 1319, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/ecommerce/E-Commerce-Website/frontend/node_modules/next/src/shared/lib/router/utils/querystring.ts"], "sourcesContent": ["import type { ParsedUrlQuery } from 'querystring'\n\nexport function searchParamsToUrlQuery(\n  searchParams: URLSearchParams\n): ParsedUrlQuery {\n  const query: ParsedUrlQuery = {}\n  for (const [key, value] of searchParams.entries()) {\n    const existing = query[key]\n    if (typeof existing === 'undefined') {\n      query[key] = value\n    } else if (Array.isArray(existing)) {\n      existing.push(value)\n    } else {\n      query[key] = [existing, value]\n    }\n  }\n  return query\n}\n\nfunction stringifyUrlQueryParam(param: unknown): string {\n  if (typeof param === 'string') {\n    return param\n  }\n\n  if (\n    (typeof param === 'number' && !isNaN(param)) ||\n    typeof param === 'boolean'\n  ) {\n    return String(param)\n  } else {\n    return ''\n  }\n}\n\nexport function urlQueryToSearchParams(query: ParsedUrlQuery): URLSearchParams {\n  const searchParams = new URLSearchParams()\n  for (const [key, value] of Object.entries(query)) {\n    if (Array.isArray(value)) {\n      for (const item of value) {\n        searchParams.append(key, stringifyUrlQueryParam(item))\n      }\n    } else {\n      searchParams.set(key, stringifyUrlQueryParam(value))\n    }\n  }\n  return searchParams\n}\n\nexport function assign(\n  target: URLSearchParams,\n  ...searchParamsList: URLSearchParams[]\n): URLSearchParams {\n  for (const searchParams of searchParamsList) {\n    for (const key of searchParams.keys()) {\n      target.delete(key)\n    }\n\n    for (const [key, value] of searchParams.entries()) {\n      target.append(key, value)\n    }\n  }\n\n  return target\n}\n"], "names": ["assign", "searchParamsToUrlQuery", "urlQueryToSearchParams", "searchParams", "query", "key", "value", "entries", "existing", "Array", "isArray", "push", "stringifyUrlQueryParam", "param", "isNaN", "String", "URLSearchParams", "Object", "item", "append", "set", "target", "searchParamsList", "keys", "delete"], "mappings": ";;;;;;;;;;;;;;;;IAgDgBA,MAAM,EAAA;eAANA;;IA9CAC,sBAAsB,EAAA;eAAtBA;;IAgCAC,sBAAsB,EAAA;eAAtBA;;;AAhCT,SAASD,uBACdE,YAA6B;IAE7B,MAAMC,QAAwB,CAAC;IAC/B,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIH,aAAaI,OAAO,GAAI;QACjD,MAAMC,WAAWJ,KAAK,CAACC,IAAI;QAC3B,IAAI,OAAOG,aAAa,aAAa;YACnCJ,KAAK,CAACC,IAAI,GAAGC;QACf,OAAO,IAAIG,MAAMC,OAAO,CAACF,WAAW;YAClCA,SAASG,IAAI,CAACL;QAChB,OAAO;YACLF,KAAK,CAACC,IAAI,GAAG;gBAACG;gBAAUF;aAAM;QAChC;IACF;IACA,OAAOF;AACT;AAEA,SAASQ,uBAAuBC,KAAc;IAC5C,IAAI,OAAOA,UAAU,UAAU;QAC7B,OAAOA;IACT;IAEA,IACG,OAAOA,UAAU,YAAY,CAACC,MAAMD,UACrC,OAAOA,UAAU,WACjB;QACA,OAAOE,OAAOF;IAChB,OAAO;QACL,OAAO;IACT;AACF;AAEO,SAASX,uBAAuBE,KAAqB;IAC1D,MAAMD,eAAe,IAAIa;IACzB,KAAK,MAAM,CAACX,KAAKC,MAAM,IAAIW,OAAOV,OAAO,CAACH,OAAQ;QAChD,IAAIK,MAAMC,OAAO,CAACJ,QAAQ;YACxB,KAAK,MAAMY,QAAQZ,MAAO;gBACxBH,aAAagB,MAAM,CAACd,KAAKO,uBAAuBM;YAClD;QACF,OAAO;YACLf,aAAaiB,GAAG,CAACf,KAAKO,uBAAuBN;QAC/C;IACF;IACA,OAAOH;AACT;AAEO,SAASH,OACdqB,MAAuB;IACvB,IAAA,IAAA,OAAA,UAAA,MAAA,EAAGC,mBAAH,IAAA,MAAA,OAAA,IAAA,OAAA,IAAA,IAAA,OAAA,GAAA,OAAA,MAAA,OAAA;QAAGA,gBAAAA,CAAH,OAAA,EAAA,GAAA,SAAA,CAAA,KAAsC;;IAEtC,KAAK,MAAMnB,gBAAgBmB,iBAAkB;QAC3C,KAAK,MAAMjB,OAAOF,aAAaoB,IAAI,GAAI;YACrCF,OAAOG,MAAM,CAACnB;QAChB;QAEA,KAAK,MAAM,CAACA,KAAKC,MAAM,IAAIH,aAAaI,OAAO,GAAI;YACjDc,OAAOF,MAAM,CAACd,KAAKC;QACrB;IACF;IAEA,OAAOe;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1404, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/ecommerce/E-Commerce-Website/frontend/node_modules/next/src/shared/lib/router/utils/format-url.ts"], "sourcesContent": ["// Format function modified from nodejs\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nimport type { UrlObject } from 'url'\nimport type { ParsedUrlQuery } from 'querystring'\nimport * as querystring from './querystring'\n\nconst slashedProtocols = /https?|ftp|gopher|file/\n\nexport function formatUrl(urlObj: UrlObject) {\n  let { auth, hostname } = urlObj\n  let protocol = urlObj.protocol || ''\n  let pathname = urlObj.pathname || ''\n  let hash = urlObj.hash || ''\n  let query = urlObj.query || ''\n  let host: string | false = false\n\n  auth = auth ? encodeURIComponent(auth).replace(/%3A/i, ':') + '@' : ''\n\n  if (urlObj.host) {\n    host = auth + urlObj.host\n  } else if (hostname) {\n    host = auth + (~hostname.indexOf(':') ? `[${hostname}]` : hostname)\n    if (urlObj.port) {\n      host += ':' + urlObj.port\n    }\n  }\n\n  if (query && typeof query === 'object') {\n    query = String(querystring.urlQueryToSearchParams(query as ParsedUrlQuery))\n  }\n\n  let search = urlObj.search || (query && `?${query}`) || ''\n\n  if (protocol && !protocol.endsWith(':')) protocol += ':'\n\n  if (\n    urlObj.slashes ||\n    ((!protocol || slashedProtocols.test(protocol)) && host !== false)\n  ) {\n    host = '//' + (host || '')\n    if (pathname && pathname[0] !== '/') pathname = '/' + pathname\n  } else if (!host) {\n    host = ''\n  }\n\n  if (hash && hash[0] !== '#') hash = '#' + hash\n  if (search && search[0] !== '?') search = '?' + search\n\n  pathname = pathname.replace(/[?#]/g, encodeURIComponent)\n  search = search.replace('#', '%23')\n\n  return `${protocol}${host}${pathname}${search}${hash}`\n}\n\nexport const urlObjectKeys = [\n  'auth',\n  'hash',\n  'host',\n  'hostname',\n  'href',\n  'path',\n  'pathname',\n  'port',\n  'protocol',\n  'query',\n  'search',\n  'slashes',\n]\n\nexport function formatWithValidation(url: UrlObject): string {\n  if (process.env.NODE_ENV === 'development') {\n    if (url !== null && typeof url === 'object') {\n      Object.keys(url).forEach((key) => {\n        if (!urlObjectKeys.includes(key)) {\n          console.warn(\n            `Unknown key passed via urlObject into url.format: ${key}`\n          )\n        }\n      })\n    }\n  }\n\n  return formatUrl(url)\n}\n"], "names": ["formatUrl", "formatWithValidation", "urlObjectKeys", "slashedProtocols", "url<PERSON>bj", "auth", "hostname", "protocol", "pathname", "hash", "query", "host", "encodeURIComponent", "replace", "indexOf", "port", "String", "querystring", "urlQueryToSearchParams", "search", "endsWith", "slashes", "test", "url", "process", "env", "NODE_ENV", "Object", "keys", "for<PERSON>ach", "key", "includes", "console", "warn"], "mappings": "AAAA,uCAAuC;AACvC,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;AAsEnCwB,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;;;;;;;;;;;;;IA9Df1B,SAAS,EAAA;eAATA;;IA6DAC,oBAAoB,EAAA;eAApBA;;IAfHC,aAAa,EAAA;eAAbA;;;;uEAlDgB;AAE7B,MAAMC,mBAAmB;AAElB,SAASH,UAAUI,MAAiB;IACzC,IAAI,EAAEC,IAAI,EAAEC,QAAQ,EAAE,GAAGF;IACzB,IAAIG,WAAWH,OAAOG,QAAQ,IAAI;IAClC,IAAIC,WAAWJ,OAAOI,QAAQ,IAAI;IAClC,IAAIC,OAAOL,OAAOK,IAAI,IAAI;IAC1B,IAAIC,QAAQN,OAAOM,KAAK,IAAI;IAC5B,IAAIC,OAAuB;IAE3BN,OAAOA,OAAOO,mBAAmBP,MAAMQ,OAAO,CAAC,QAAQ,OAAO,MAAM;IAEpE,IAAIT,OAAOO,IAAI,EAAE;QACfA,OAAON,OAAOD,OAAOO,IAAI;IAC3B,OAAO,IAAIL,UAAU;QACnBK,OAAON,OAAQ,CAAA,CAACC,SAASQ,OAAO,CAAC,OAAQ,MAAGR,WAAS,MAAKA,QAAO;QACjE,IAAIF,OAAOW,IAAI,EAAE;YACfJ,QAAQ,MAAMP,OAAOW,IAAI;QAC3B;IACF;IAEA,IAAIL,SAAS,OAAOA,UAAU,UAAU;QACtCA,QAAQM,OAAOC,aAAYC,sBAAsB,CAACR;IACpD;IAEA,IAAIS,SAASf,OAAOe,MAAM,IAAKT,SAAU,MAAGA,SAAY;IAExD,IAAIH,YAAY,CAACA,SAASa,QAAQ,CAAC,MAAMb,YAAY;IAErD,IACEH,OAAOiB,OAAO,IACZ,CAAA,CAACd,YAAYJ,iBAAiBmB,IAAI,CAACf,SAAQ,KAAMI,SAAS,OAC5D;QACAA,OAAO,OAAQA,CAAAA,QAAQ,EAAC;QACxB,IAAIH,YAAYA,QAAQ,CAAC,EAAE,KAAK,KAAKA,WAAW,MAAMA;IACxD,OAAO,IAAI,CAACG,MAAM;QAChBA,OAAO;IACT;IAEA,IAAIF,QAAQA,IAAI,CAAC,EAAE,KAAK,KAAKA,OAAO,MAAMA;IAC1C,IAAIU,UAAUA,MAAM,CAAC,EAAE,KAAK,KAAKA,SAAS,MAAMA;IAEhDX,WAAWA,SAASK,OAAO,CAAC,SAASD;IACrCO,SAASA,OAAON,OAAO,CAAC,KAAK;IAE7B,OAAQ,KAAEN,WAAWI,OAAOH,WAAWW,SAASV;AAClD;AAEO,MAAMP,gBAAgB;IAC3B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,SAASD,qBAAqBsB,GAAc;IACjD,wCAA4C;QAC1C,IAAIA,QAAQ,QAAQ,OAAOA,QAAQ,UAAU;YAC3CI,OAAOC,IAAI,CAACL,KAAKM,OAAO,CAAC,CAACC;gBACxB,IAAI,CAAC5B,cAAc6B,QAAQ,CAACD,MAAM;oBAChCE,QAAQC,IAAI,CACT,uDAAoDH;gBAEzD;YACF;QACF;IACF;IAEA,OAAO9B,UAAUuB;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1519, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/ecommerce/E-Commerce-Website/frontend/node_modules/next/src/client/use-merged-ref.ts"], "sourcesContent": ["import { useCallback, useRef, type Ref } from 'react'\n\n// This is a compatibility hook to support React 18 and 19 refs.\n// In 19, a cleanup function from refs may be returned.\n// In 18, returning a cleanup function creates a warning.\n// Since we take userspace refs, we don't know ahead of time if a cleanup function will be returned.\n// This implements cleanup functions with the old behavior in 18.\n// We know refs are always called alternating with `null` and then `T`.\n// So a call with `null` means we need to call the previous cleanup functions.\nexport function useMergedRef<TElement>(\n  refA: Ref<TElement>,\n  refB: Ref<TElement>\n): Ref<TElement> {\n  const cleanupA = useRef<(() => void) | null>(null)\n  const cleanupB = useRef<(() => void) | null>(null)\n\n  // NOTE: In theory, we could skip the wrapping if only one of the refs is non-null.\n  // (this happens often if the user doesn't pass a ref to Link/Form/Image)\n  // But this can cause us to leak a cleanup-ref into user code (e.g. via `<Link legacyBehavior>`),\n  // and the user might pass that ref into ref-merging library that doesn't support cleanup refs\n  // (because it hasn't been updated for React 19)\n  // which can then cause things to blow up, because a cleanup-returning ref gets called with `null`.\n  // So in practice, it's safer to be defensive and always wrap the ref, even on React 19.\n  return useCallback(\n    (current: TElement | null): void => {\n      if (current === null) {\n        const cleanupFnA = cleanupA.current\n        if (cleanupFnA) {\n          cleanupA.current = null\n          cleanupFnA()\n        }\n        const cleanupFnB = cleanupB.current\n        if (cleanupFnB) {\n          cleanupB.current = null\n          cleanupFnB()\n        }\n      } else {\n        if (refA) {\n          cleanupA.current = applyRef(refA, current)\n        }\n        if (refB) {\n          cleanupB.current = applyRef(refB, current)\n        }\n      }\n    },\n    [refA, refB]\n  )\n}\n\nfunction applyRef<TElement>(\n  refA: NonNullable<Ref<TElement>>,\n  current: TElement\n) {\n  if (typeof refA === 'function') {\n    const cleanup = refA(current)\n    if (typeof cleanup === 'function') {\n      return cleanup\n    } else {\n      return () => refA(null)\n    }\n  } else {\n    refA.current = current\n    return () => {\n      refA.current = null\n    }\n  }\n}\n"], "names": ["useMergedRef", "refA", "refB", "cleanupA", "useRef", "cleanupB", "useCallback", "current", "cleanupFnA", "cleanupFnB", "applyRef", "cleanup"], "mappings": ";;;;+BASgBA,gBAAAA;;;eAAAA;;;uBAT8B;AASvC,SAASA,aACdC,IAAmB,EACnBC,IAAmB;IAEnB,MAAMC,WAAWC,CAAAA,GAAAA,OAAAA,MAAM,EAAsB;IAC7C,MAAMC,WAAWD,CAAAA,GAAAA,OAAAA,MAAM,EAAsB;IAE7C,mFAAmF;IACnF,yEAAyE;IACzE,iGAAiG;IACjG,8FAA8F;IAC9F,gDAAgD;IAChD,mGAAmG;IACnG,wFAAwF;IACxF,OAAOE,CAAAA,GAAAA,OAAAA,WAAW,EAChB,CAACC;QACC,IAAIA,YAAY,MAAM;YACpB,MAAMC,aAAaL,SAASI,OAAO;YACnC,IAAIC,YAAY;gBACdL,SAASI,OAAO,GAAG;gBACnBC;YACF;YACA,MAAMC,aAAaJ,SAASE,OAAO;YACnC,IAAIE,YAAY;gBACdJ,SAASE,OAAO,GAAG;gBACnBE;YACF;QACF,OAAO;YACL,IAAIR,MAAM;gBACRE,SAASI,OAAO,GAAGG,SAAST,MAAMM;YACpC;YACA,IAAIL,MAAM;gBACRG,SAASE,OAAO,GAAGG,SAASR,MAAMK;YACpC;QACF;IACF,GACA;QAACN;QAAMC;KAAK;AAEhB;AAEA,SAASQ,SACPT,IAAgC,EAChCM,OAAiB;IAEjB,IAAI,OAAON,SAAS,YAAY;QAC9B,MAAMU,UAAUV,KAAKM;QACrB,IAAI,OAAOI,YAAY,YAAY;YACjC,OAAOA;QACT,OAAO;YACL,OAAO,IAAMV,KAAK;QACpB;IACF,OAAO;QACLA,KAAKM,OAAO,GAAGA;QACf,OAAO;YACLN,KAAKM,OAAO,GAAG;QACjB;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1592, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/ecommerce/E-Commerce-Website/frontend/node_modules/next/src/shared/lib/utils.ts"], "sourcesContent": ["import type { HtmlProps } from './html-context.shared-runtime'\nimport type { ComponentType, JSX } from 'react'\nimport type { DomainLocale } from '../../server/config'\nimport type { Env } from '@next/env'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type { NextRouter } from './router/router'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { PreviewData } from '../../types'\nimport type { COMPILER_NAMES } from './constants'\nimport type fs from 'fs'\n\nexport type NextComponentType<\n  Context extends BaseContext = NextPageContext,\n  InitialProps = {},\n  Props = {},\n> = ComponentType<Props> & {\n  /**\n   * Used for initial page load data population. Data returned from `getInitialProps` is serialized when server rendered.\n   * Make sure to return plain `Object` without using `Date`, `Map`, `Set`.\n   * @param context Context of `page`\n   */\n  getInitialProps?(context: Context): InitialProps | Promise<InitialProps>\n}\n\nexport type DocumentType = NextComponentType<\n  DocumentContext,\n  DocumentInitialProps,\n  DocumentProps\n>\n\nexport type AppType<P = {}> = NextComponentType<\n  AppContextType,\n  P,\n  AppPropsType<any, P>\n>\n\nexport type AppTreeType = ComponentType<\n  AppInitialProps & { [name: string]: any }\n>\n\n/**\n * Web vitals provided to _app.reportWebVitals by Core Web Vitals plugin developed by Google Chrome team.\n * https://nextjs.org/blog/next-9-4#integrated-web-vitals-reporting\n */\nexport const WEB_VITALS = ['CLS', 'FCP', 'FID', 'INP', 'LCP', 'TTFB'] as const\nexport type NextWebVitalsMetric = {\n  id: string\n  startTime: number\n  value: number\n  attribution?: { [key: string]: unknown }\n} & (\n  | {\n      label: 'web-vital'\n      name: (typeof WEB_VITALS)[number]\n    }\n  | {\n      label: 'custom'\n      name:\n        | 'Next.js-hydration'\n        | 'Next.js-route-change-to-render'\n        | 'Next.js-render'\n    }\n)\n\nexport type Enhancer<C> = (Component: C) => C\n\nexport type ComponentsEnhancer =\n  | {\n      enhanceApp?: Enhancer<AppType>\n      enhanceComponent?: Enhancer<NextComponentType>\n    }\n  | Enhancer<NextComponentType>\n\nexport type RenderPageResult = {\n  html: string\n  head?: Array<JSX.Element | null>\n}\n\nexport type RenderPage = (\n  options?: ComponentsEnhancer\n) => DocumentInitialProps | Promise<DocumentInitialProps>\n\nexport type BaseContext = {\n  res?: ServerResponse\n  [k: string]: any\n}\n\nexport type NEXT_DATA = {\n  props: Record<string, any>\n  page: string\n  query: ParsedUrlQuery\n  buildId: string\n  assetPrefix?: string\n  runtimeConfig?: { [key: string]: any }\n  nextExport?: boolean\n  autoExport?: boolean\n  isFallback?: boolean\n  isExperimentalCompile?: boolean\n  dynamicIds?: (string | number)[]\n  err?: Error & {\n    statusCode?: number\n    source?: typeof COMPILER_NAMES.server | typeof COMPILER_NAMES.edgeServer\n  }\n  gsp?: boolean\n  gssp?: boolean\n  customServer?: boolean\n  gip?: boolean\n  appGip?: boolean\n  locale?: string\n  locales?: readonly string[]\n  defaultLocale?: string\n  domainLocales?: readonly DomainLocale[]\n  scriptLoader?: any[]\n  isPreview?: boolean\n  notFoundSrcPage?: string\n}\n\n/**\n * `Next` context\n */\nexport interface NextPageContext {\n  /**\n   * Error object if encountered during rendering\n   */\n  err?: (Error & { statusCode?: number }) | null\n  /**\n   * `HTTP` request object.\n   */\n  req?: IncomingMessage\n  /**\n   * `HTTP` response object.\n   */\n  res?: ServerResponse\n  /**\n   * Path section of `URL`.\n   */\n  pathname: string\n  /**\n   * Query string section of `URL` parsed as an object.\n   */\n  query: ParsedUrlQuery\n  /**\n   * `String` of the actual path including query.\n   */\n  asPath?: string\n  /**\n   * The currently active locale\n   */\n  locale?: string\n  /**\n   * All configured locales\n   */\n  locales?: readonly string[]\n  /**\n   * The configured default locale\n   */\n  defaultLocale?: string\n  /**\n   * `Component` the tree of the App to use if needing to render separately\n   */\n  AppTree: AppTreeType\n}\n\nexport type AppContextType<Router extends NextRouter = NextRouter> = {\n  Component: NextComponentType<NextPageContext>\n  AppTree: AppTreeType\n  ctx: NextPageContext\n  router: Router\n}\n\nexport type AppInitialProps<PageProps = any> = {\n  pageProps: PageProps\n}\n\nexport type AppPropsType<\n  Router extends NextRouter = NextRouter,\n  PageProps = {},\n> = AppInitialProps<PageProps> & {\n  Component: NextComponentType<NextPageContext, any, any>\n  router: Router\n  __N_SSG?: boolean\n  __N_SSP?: boolean\n}\n\nexport type DocumentContext = NextPageContext & {\n  renderPage: RenderPage\n  defaultGetInitialProps(\n    ctx: DocumentContext,\n    options?: { nonce?: string }\n  ): Promise<DocumentInitialProps>\n}\n\nexport type DocumentInitialProps = RenderPageResult & {\n  styles?: React.ReactElement[] | Iterable<React.ReactNode> | JSX.Element\n}\n\nexport type DocumentProps = DocumentInitialProps & HtmlProps\n\n/**\n * Next `API` route request\n */\nexport interface NextApiRequest extends IncomingMessage {\n  /**\n   * Object of `query` values from url\n   */\n  query: Partial<{\n    [key: string]: string | string[]\n  }>\n  /**\n   * Object of `cookies` from header\n   */\n  cookies: Partial<{\n    [key: string]: string\n  }>\n\n  body: any\n\n  env: Env\n\n  draftMode?: boolean\n\n  preview?: boolean\n  /**\n   * Preview data set on the request, if any\n   * */\n  previewData?: PreviewData\n}\n\n/**\n * Send body of response\n */\ntype Send<T> = (body: T) => void\n\n/**\n * Next `API` route response\n */\nexport type NextApiResponse<Data = any> = ServerResponse & {\n  /**\n   * Send data `any` data in response\n   */\n  send: Send<Data>\n  /**\n   * Send data `json` data in response\n   */\n  json: Send<Data>\n  status: (statusCode: number) => NextApiResponse<Data>\n  redirect(url: string): NextApiResponse<Data>\n  redirect(status: number, url: string): NextApiResponse<Data>\n\n  /**\n   * Set draft mode\n   */\n  setDraftMode: (options: { enable: boolean }) => NextApiResponse<Data>\n\n  /**\n   * Set preview data for Next.js' prerender mode\n   */\n  setPreviewData: (\n    data: object | string,\n    options?: {\n      /**\n       * Specifies the number (in seconds) for the preview session to last for.\n       * The given number will be converted to an integer by rounding down.\n       * By default, no maximum age is set and the preview session finishes\n       * when the client shuts down (browser is closed).\n       */\n      maxAge?: number\n      /**\n       * Specifies the path for the preview session to work under. By default,\n       * the path is considered the \"default path\", i.e., any pages under \"/\".\n       */\n      path?: string\n    }\n  ) => NextApiResponse<Data>\n\n  /**\n   * Clear preview data for Next.js' prerender mode\n   */\n  clearPreviewData: (options?: { path?: string }) => NextApiResponse<Data>\n\n  /**\n   * Revalidate a specific page and regenerate it using On-Demand Incremental\n   * Static Regeneration.\n   * The path should be an actual path, not a rewritten path. E.g. for\n   * \"/blog/[slug]\" this should be \"/blog/post-1\".\n   * @link https://nextjs.org/docs/app/building-your-application/data-fetching/incremental-static-regeneration#on-demand-revalidation-with-revalidatepath\n   */\n  revalidate: (\n    urlPath: string,\n    opts?: {\n      unstable_onlyGenerated?: boolean\n    }\n  ) => Promise<void>\n}\n\n/**\n * Next `API` route handler\n */\nexport type NextApiHandler<T = any> = (\n  req: NextApiRequest,\n  res: NextApiResponse<T>\n) => unknown | Promise<unknown>\n\n/**\n * Utils\n */\nexport function execOnce<T extends (...args: any[]) => ReturnType<T>>(\n  fn: T\n): T {\n  let used = false\n  let result: ReturnType<T>\n\n  return ((...args: any[]) => {\n    if (!used) {\n      used = true\n      result = fn(...args)\n    }\n    return result\n  }) as T\n}\n\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/\nexport const isAbsoluteUrl = (url: string) => ABSOLUTE_URL_REGEX.test(url)\n\nexport function getLocationOrigin() {\n  const { protocol, hostname, port } = window.location\n  return `${protocol}//${hostname}${port ? ':' + port : ''}`\n}\n\nexport function getURL() {\n  const { href } = window.location\n  const origin = getLocationOrigin()\n  return href.substring(origin.length)\n}\n\nexport function getDisplayName<P>(Component: ComponentType<P>) {\n  return typeof Component === 'string'\n    ? Component\n    : Component.displayName || Component.name || 'Unknown'\n}\n\nexport function isResSent(res: ServerResponse) {\n  return res.finished || res.headersSent\n}\n\nexport function normalizeRepeatedSlashes(url: string) {\n  const urlParts = url.split('?')\n  const urlNoQuery = urlParts[0]\n\n  return (\n    urlNoQuery\n      // first we replace any non-encoded backslashes with forward\n      // then normalize repeated forward slashes\n      .replace(/\\\\/g, '/')\n      .replace(/\\/\\/+/g, '/') +\n    (urlParts[1] ? `?${urlParts.slice(1).join('?')}` : '')\n  )\n}\n\nexport async function loadGetInitialProps<\n  C extends BaseContext,\n  IP = {},\n  P = {},\n>(App: NextComponentType<C, IP, P>, ctx: C): Promise<IP> {\n  if (process.env.NODE_ENV !== 'production') {\n    if (App.prototype?.getInitialProps) {\n      const message = `\"${getDisplayName(\n        App\n      )}.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.`\n      throw new Error(message)\n    }\n  }\n  // when called from _app `ctx` is nested in `ctx`\n  const res = ctx.res || (ctx.ctx && ctx.ctx.res)\n\n  if (!App.getInitialProps) {\n    if (ctx.ctx && ctx.Component) {\n      // @ts-ignore pageProps default\n      return {\n        pageProps: await loadGetInitialProps(ctx.Component, ctx.ctx),\n      }\n    }\n    return {} as IP\n  }\n\n  const props = await App.getInitialProps(ctx)\n\n  if (res && isResSent(res)) {\n    return props\n  }\n\n  if (!props) {\n    const message = `\"${getDisplayName(\n      App\n    )}.getInitialProps()\" should resolve to an object. But found \"${props}\" instead.`\n    throw new Error(message)\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (Object.keys(props).length === 0 && !ctx.ctx) {\n      console.warn(\n        `${getDisplayName(\n          App\n        )} returned an empty object from \\`getInitialProps\\`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps`\n      )\n    }\n  }\n\n  return props\n}\n\nexport const SP = typeof performance !== 'undefined'\nexport const ST =\n  SP &&\n  (['mark', 'measure', 'getEntriesByName'] as const).every(\n    (method) => typeof performance[method] === 'function'\n  )\n\nexport class DecodeError extends Error {}\nexport class NormalizeError extends Error {}\nexport class PageNotFoundError extends Error {\n  code: string\n\n  constructor(page: string) {\n    super()\n    this.code = 'ENOENT'\n    this.name = 'PageNotFoundError'\n    this.message = `Cannot find module for page: ${page}`\n  }\n}\n\nexport class MissingStaticPage extends Error {\n  constructor(page: string, message: string) {\n    super()\n    this.message = `Failed to load static file for page: ${page} ${message}`\n  }\n}\n\nexport class MiddlewareNotFoundError extends Error {\n  code: string\n  constructor() {\n    super()\n    this.code = 'ENOENT'\n    this.message = `Cannot find the middleware module`\n  }\n}\n\nexport interface CacheFs {\n  existsSync: typeof fs.existsSync\n  readFile: typeof fs.promises.readFile\n  readFileSync: typeof fs.readFileSync\n  writeFile(f: string, d: any): Promise<void>\n  mkdir(dir: string): Promise<void | string>\n  stat(f: string): Promise<{ mtime: Date }>\n}\n\nexport function stringifyError(error: Error) {\n  return JSON.stringify({ message: error.message, stack: error.stack })\n}\n"], "names": ["DecodeError", "MiddlewareNotFoundError", "MissingStaticPage", "NormalizeError", "PageNotFoundError", "SP", "ST", "WEB_VITALS", "execOnce", "getDisplayName", "getLocationOrigin", "getURL", "isAbsoluteUrl", "isResSent", "loadGetInitialProps", "normalizeRepeatedSlashes", "stringifyError", "fn", "used", "result", "args", "ABSOLUTE_URL_REGEX", "url", "test", "protocol", "hostname", "port", "window", "location", "href", "origin", "substring", "length", "Component", "displayName", "name", "res", "finished", "headersSent", "urlParts", "split", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "replace", "slice", "join", "App", "ctx", "process", "env", "NODE_ENV", "prototype", "getInitialProps", "message", "Error", "pageProps", "props", "Object", "keys", "console", "warn", "performance", "every", "method", "constructor", "page", "code", "error", "JSON", "stringify", "stack"], "mappings": "AA8WM+C,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAsDlBjD,WAAW,EAAA;eAAXA;;IAoBAC,uBAAuB,EAAA;eAAvBA;;IAPAC,iBAAiB,EAAA;eAAjBA;;IAZAC,cAAc,EAAA;eAAdA;;IACAC,iBAAiB,EAAA;eAAjBA;;IATAC,EAAE,EAAA;eAAFA;;IACAC,EAAE,EAAA;eAAFA;;IAlXAC,UAAU,EAAA;eAAVA;;IAsQGC,QAAQ,EAAA;eAARA;;IA+BAC,cAAc,EAAA;eAAdA;;IAXAC,iBAAiB,EAAA;eAAjBA;;IAKAC,MAAM,EAAA;eAANA;;IAPHC,aAAa,EAAA;eAAbA;;IAmBGC,SAAS,EAAA;eAATA;;IAkBMC,mBAAmB,EAAA;eAAnBA;;IAdNC,wBAAwB,EAAA;eAAxBA;;IA+GAC,cAAc,EAAA;eAAdA;;;AA9ZT,MAAMT,aAAa;IAAC;IAAO;IAAO;IAAO;IAAO;IAAO;CAAO;AAsQ9D,SAASC,SACdS,EAAK;IAEL,IAAIC,OAAO;IACX,IAAIC;IAEJ,OAAQ;yCAAIC,OAAAA,IAAAA,MAAAA,OAAAA,OAAAA,GAAAA,OAAAA,MAAAA,OAAAA;YAAAA,IAAAA,CAAAA,KAAAA,GAAAA,SAAAA,CAAAA,KAAAA;;QACV,IAAI,CAACF,MAAM;YACTA,OAAO;YACPC,SAASF,MAAMG;QACjB;QACA,OAAOD;IACT;AACF;AAEA,0DAA0D;AAC1D,gEAAgE;AAChE,MAAME,qBAAqB;AACpB,MAAMT,gBAAgB,CAACU,MAAgBD,mBAAmBE,IAAI,CAACD;AAE/D,SAASZ;IACd,MAAM,EAAEc,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,EAAE,GAAGC,OAAOC,QAAQ;IACpD,OAAUJ,WAAS,OAAIC,WAAWC,CAAAA,OAAO,MAAMA,OAAO,EAAC;AACzD;AAEO,SAASf;IACd,MAAM,EAAEkB,IAAI,EAAE,GAAGF,OAAOC,QAAQ;IAChC,MAAME,SAASpB;IACf,OAAOmB,KAAKE,SAAS,CAACD,OAAOE,MAAM;AACrC;AAEO,SAASvB,eAAkBwB,SAA2B;IAC3D,OAAO,OAAOA,cAAc,WACxBA,YACAA,UAAUC,WAAW,IAAID,UAAUE,IAAI,IAAI;AACjD;AAEO,SAAStB,UAAUuB,GAAmB;IAC3C,OAAOA,IAAIC,QAAQ,IAAID,IAAIE,WAAW;AACxC;AAEO,SAASvB,yBAAyBO,GAAW;IAClD,MAAMiB,WAAWjB,IAAIkB,KAAK,CAAC;IAC3B,MAAMC,aAAaF,QAAQ,CAAC,EAAE;IAE9B,OACEE,WACE,4DAA4D;IAC5D,0CAA0C;KACzCC,OAAO,CAAC,OAAO,KACfA,OAAO,CAAC,UAAU,OACpBH,CAAAA,QAAQ,CAAC,EAAE,GAAI,MAAGA,SAASI,KAAK,CAAC,GAAGC,IAAI,CAAC,OAAS,EAAC;AAExD;AAEO,eAAe9B,oBAIpB+B,GAAgC,EAAEC,GAAM;IACxC,wCAA2C;YACrCD;QAAJ,IAAA,CAAIA,iBAAAA,IAAIK,SAAS,KAAA,OAAA,KAAA,IAAbL,eAAeM,eAAe,EAAE;YAClC,MAAMC,UAAW,MAAG3C,eAClBoC,OACA;YACF,MAAM,OAAA,cAAkB,CAAlB,IAAIQ,MAAMD,UAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAiB;QACzB;IACF;IACA,iDAAiD;IACjD,MAAMhB,MAAMU,IAAIV,GAAG,IAAKU,IAAIA,GAAG,IAAIA,IAAIA,GAAG,CAACV,GAAG;IAE9C,IAAI,CAACS,IAAIM,eAAe,EAAE;QACxB,IAAIL,IAAIA,GAAG,IAAIA,IAAIb,SAAS,EAAE;YAC5B,+BAA+B;YAC/B,OAAO;gBACLqB,WAAW,MAAMxC,oBAAoBgC,IAAIb,SAAS,EAAEa,IAAIA,GAAG;YAC7D;QACF;QACA,OAAO,CAAC;IACV;IAEA,MAAMS,QAAQ,MAAMV,IAAIM,eAAe,CAACL;IAExC,IAAIV,OAAOvB,UAAUuB,MAAM;QACzB,OAAOmB;IACT;IAEA,IAAI,CAACA,OAAO;QACV,MAAMH,UAAW,MAAG3C,eAClBoC,OACA,iEAA8DU,QAAM;QACtE,MAAM,OAAA,cAAkB,CAAlB,IAAIF,MAAMD,UAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAiB;IACzB;IAEA,IAAIL,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;QACzC,IAAIO,OAAOC,IAAI,CAACF,OAAOvB,MAAM,KAAK,KAAK,CAACc,IAAIA,GAAG,EAAE;YAC/CY,QAAQC,IAAI,CACT,KAAElD,eACDoC,OACA;QAEN;IACF;IAEA,OAAOU;AACT;AAEO,MAAMlD,KAAK,OAAOuD,gBAAgB;AAClC,MAAMtD,KACXD,MACC;IAAC;IAAQ;IAAW;CAAmB,CAAWwD,KAAK,CACtD,CAACC,SAAW,OAAOF,WAAW,CAACE,OAAO,KAAK;AAGxC,MAAM9D,oBAAoBqD;AAAO;AACjC,MAAMlD,uBAAuBkD;AAAO;AACpC,MAAMjD,0BAA0BiD;IAGrCU,YAAYC,IAAY,CAAE;QACxB,KAAK;QACL,IAAI,CAACC,IAAI,GAAG;QACZ,IAAI,CAAC9B,IAAI,GAAG;QACZ,IAAI,CAACiB,OAAO,GAAI,kCAA+BY;IACjD;AACF;AAEO,MAAM9D,0BAA0BmD;IACrCU,YAAYC,IAAY,EAAEZ,OAAe,CAAE;QACzC,KAAK;QACL,IAAI,CAACA,OAAO,GAAI,0CAAuCY,OAAK,MAAGZ;IACjE;AACF;AAEO,MAAMnD,gCAAgCoD;IAE3CU,aAAc;QACZ,KAAK;QACL,IAAI,CAACE,IAAI,GAAG;QACZ,IAAI,CAACb,OAAO,GAAI;IAClB;AACF;AAWO,SAASpC,eAAekD,KAAY;IACzC,OAAOC,KAAKC,SAAS,CAAC;QAAEhB,SAASc,MAAMd,OAAO;QAAEiB,OAAOH,MAAMG,KAAK;IAAC;AACrE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1807, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/ecommerce/E-Commerce-Website/frontend/node_modules/next/src/shared/lib/router/utils/is-local-url.ts"], "sourcesContent": ["import { isAbsoluteUrl, getLocationOrigin } from '../../utils'\nimport { hasBasePath } from '../../../../client/has-base-path'\n\n/**\n * Detects whether a given url is routable by the Next.js router (browser only).\n */\nexport function isLocalURL(url: string): boolean {\n  // prevent a hydration mismatch on href for url with anchor refs\n  if (!isAbsoluteUrl(url)) return true\n  try {\n    // absolute urls can be local if they are on the same origin\n    const locationOrigin = getLocationOrigin()\n    const resolved = new URL(url, locationOrigin)\n    return resolved.origin === locationOrigin && hasBasePath(resolved.pathname)\n  } catch (_) {\n    return false\n  }\n}\n"], "names": ["isLocalURL", "url", "isAbsoluteUrl", "locationOrigin", "getLocationOrigin", "resolved", "URL", "origin", "has<PERSON>ase<PERSON><PERSON>", "pathname", "_"], "mappings": ";;;;+BAMgBA,cAAAA;;;eAAAA;;;uBANiC;6BACrB;AAKrB,SAASA,WAAWC,GAAW;IACpC,gEAAgE;IAChE,IAAI,CAACC,CAAAA,GAAAA,OAAAA,aAAa,EAACD,MAAM,OAAO;IAChC,IAAI;QACF,4DAA4D;QAC5D,MAAME,iBAAiBC,CAAAA,GAAAA,OAAAA,iBAAiB;QACxC,MAAMC,WAAW,IAAIC,IAAIL,KAAKE;QAC9B,OAAOE,SAASE,MAAM,KAAKJ,kBAAkBK,CAAAA,GAAAA,aAAAA,WAAW,EAACH,SAASI,QAAQ;IAC5E,EAAE,OAAOC,GAAG;QACV,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1836, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/ecommerce/E-Commerce-Website/frontend/node_modules/next/src/shared/lib/utils/error-once.ts"], "sourcesContent": ["let errorOnce = (_: string) => {}\nif (process.env.NODE_ENV !== 'production') {\n  const errors = new Set<string>()\n  errorOnce = (msg: string) => {\n    if (!errors.has(msg)) {\n      console.error(msg)\n    }\n    errors.add(msg)\n  }\n}\n\nexport { errorOnce }\n"], "names": ["errorOnce", "_", "process", "env", "NODE_ENV", "errors", "Set", "msg", "has", "console", "error", "add"], "mappings": "AACIE,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;+BAUpBJ,aAAAA;;;eAAAA;;;AAXT,IAAIA,YAAY,CAACC,KAAe;AAChC,wCAA2C;IACzC,MAAMI,SAAS,IAAIC;IACnBN,YAAY,CAACO;QACX,IAAI,CAACF,OAAOG,GAAG,CAACD,MAAM;YACpBE,QAAQC,KAAK,CAACH;QAChB;QACAF,OAAOM,GAAG,CAACJ;IACb;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1862, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/ecommerce/E-Commerce-Website/frontend/node_modules/next/src/client/app-dir/link.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useOptimistic, useRef } from 'react'\nimport type { UrlObject } from 'url'\nimport { formatUrl } from '../../shared/lib/router/utils/format-url'\nimport { AppRouterContext } from '../../shared/lib/app-router-context.shared-runtime'\nimport { PrefetchKind } from '../components/router-reducer/router-reducer-types'\nimport { useMergedRef } from '../use-merged-ref'\nimport { isAbsoluteUrl } from '../../shared/lib/utils'\nimport { addBasePath } from '../add-base-path'\nimport { warnOnce } from '../../shared/lib/utils/warn-once'\nimport type { PENDING_LINK_STATUS } from '../components/links'\nimport {\n  IDLE_LINK_STATUS,\n  mountLinkInstance,\n  onNavigationIntent,\n  unmountLinkForCurrentNavigation,\n  unmountPrefetchableInstance,\n  type LinkInstance,\n} from '../components/links'\nimport { isLocalURL } from '../../shared/lib/router/utils/is-local-url'\nimport { dispatchNavigateAction } from '../components/app-router-instance'\nimport { errorOnce } from '../../shared/lib/utils/error-once'\n\ntype Url = string | UrlObject\ntype RequiredKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? never : K\n}[keyof T]\ntype OptionalKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? K : never\n}[keyof T]\n\ntype OnNavigateEventHandler = (event: { preventDefault: () => void }) => void\n\ntype InternalLinkProps = {\n  /**\n   * **Required**. The path or URL to navigate to. It can also be an object (similar to `URL`).\n   *\n   * @example\n   * ```tsx\n   * // Navigate to /dashboard:\n   * <Link href=\"/dashboard\">Dashboard</Link>\n   *\n   * // Navigate to /about?name=test:\n   * <Link href={{ pathname: '/about', query: { name: 'test' } }}>\n   *   About\n   * </Link>\n   * ```\n   *\n   * @remarks\n   * - For external URLs, use a fully qualified URL such as `https://...`.\n   * - In the App Router, dynamic routes must not include bracketed segments in `href`.\n   */\n  href: Url\n\n  /**\n   * @deprecated v10.0.0: `href` props pointing to a dynamic route are\n   * automatically resolved and no longer require the `as` prop.\n   */\n  as?: Url\n\n  /**\n   * Replace the current `history` state instead of adding a new URL into the stack.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/about\" replace>\n   *   About (replaces the history state)\n   * </Link>\n   * ```\n   */\n  replace?: boolean\n\n  /**\n   * Whether to override the default scroll behavior. If `true`, Next.js attempts to maintain\n   * the scroll position if the newly navigated page is still visible. If not, it scrolls to the top.\n   *\n   * If `false`, Next.js will not modify the scroll behavior at all.\n   *\n   * @defaultValue `true`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" scroll={false}>\n   *   No auto scroll\n   * </Link>\n   * ```\n   */\n  scroll?: boolean\n\n  /**\n   * Update the path of the current page without rerunning data fetching methods\n   * like `getStaticProps`, `getServerSideProps`, or `getInitialProps`.\n   *\n   * @remarks\n   * `shallow` only applies to the Pages Router. For the App Router, see the\n   * [following documentation](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#using-the-native-history-api).\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/blog\" shallow>\n   *   Shallow navigation\n   * </Link>\n   * ```\n   */\n  shallow?: boolean\n\n  /**\n   * Forces `Link` to pass its `href` to the child component. Useful if the child is a custom\n   * component that wraps an `<a>` tag, or if you're using certain styling libraries.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" passHref>\n   *   <MyStyledAnchor>Dashboard</MyStyledAnchor>\n   * </Link>\n   * ```\n   */\n  passHref?: boolean\n\n  /**\n   * Prefetch the page in the background.\n   * Any `<Link />` that is in the viewport (initially or through scroll) will be prefetched.\n   * Prefetch can be disabled by passing `prefetch={false}`.\n   *\n   * @remarks\n   * Prefetching is only enabled in production.\n   *\n   * - In the **App Router**:\n   *   - `null` (default): Prefetch behavior depends on static vs dynamic routes:\n   *     - Static routes: fully prefetched\n   *     - Dynamic routes: partial prefetch to the nearest segment with a `loading.js`\n   *   - `true`: Always prefetch the full route and data.\n   *   - `false`: Disable prefetching on both viewport and hover.\n   * - In the **Pages Router**:\n   *   - `true` (default): Prefetches the route and data in the background on viewport or hover.\n   *   - `false`: Prefetch only on hover, not on viewport.\n   *\n   * @defaultValue `true` (Pages Router) or `null` (App Router)\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" prefetch={false}>\n   *   Dashboard\n   * </Link>\n   * ```\n   */\n  prefetch?: boolean | null\n\n  /**\n   * (unstable) Switch to a dynamic prefetch on hover. Effectively the same as\n   * updating the prefetch prop to `true` in a mouse event.\n   */\n  unstable_dynamicOnHover?: boolean\n\n  /**\n   * The active locale is automatically prepended in the Pages Router. `locale` allows for providing\n   * a different locale, or can be set to `false` to opt out of automatic locale behavior.\n   *\n   * @remarks\n   * Note: locale only applies in the Pages Router and is ignored in the App Router.\n   *\n   * @example\n   * ```tsx\n   * // Use the 'fr' locale:\n   * <Link href=\"/about\" locale=\"fr\">\n   *   About (French)\n   * </Link>\n   *\n   * // Disable locale prefix:\n   * <Link href=\"/about\" locale={false}>\n   *   About (no locale prefix)\n   * </Link>\n   * ```\n   */\n  locale?: string | false\n\n  /**\n   * Enable legacy link behavior, requiring an `<a>` tag to wrap the child content\n   * if the child is a string or number.\n   *\n   * @deprecated This will be removed in v16\n   * @defaultValue `false`\n   * @see https://github.com/vercel/next.js/commit/489e65ed98544e69b0afd7e0cfc3f9f6c2b803b7\n   */\n  legacyBehavior?: boolean\n\n  /**\n   * Optional event handler for when the mouse pointer is moved onto the `<Link>`.\n   */\n  onMouseEnter?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is touched.\n   */\n  onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is clicked.\n   */\n  onClick?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is navigated.\n   */\n  onNavigate?: OnNavigateEventHandler\n}\n\n// TODO-APP: Include the full set of Anchor props\n// adding this to the publicly exported type currently breaks existing apps\n\n// `RouteInferType` is a stub here to avoid breaking `typedRoutes` when the type\n// isn't generated yet. It will be replaced when the webpack plugin runs.\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport type LinkProps<RouteInferType = any> = InternalLinkProps\ntype LinkPropsRequired = RequiredKeys<LinkProps>\ntype LinkPropsOptional = OptionalKeys<Omit<InternalLinkProps, 'locale'>>\n\nfunction isModifiedEvent(event: React.MouseEvent): boolean {\n  const eventTarget = event.currentTarget as HTMLAnchorElement | SVGAElement\n  const target = eventTarget.getAttribute('target')\n  return (\n    (target && target !== '_self') ||\n    event.metaKey ||\n    event.ctrlKey ||\n    event.shiftKey ||\n    event.altKey || // triggers resource download\n    (event.nativeEvent && event.nativeEvent.which === 2)\n  )\n}\n\nfunction linkClicked(\n  e: React.MouseEvent,\n  href: string,\n  as: string,\n  linkInstanceRef: React.RefObject<LinkInstance | null>,\n  replace?: boolean,\n  scroll?: boolean,\n  onNavigate?: OnNavigateEventHandler\n): void {\n  const { nodeName } = e.currentTarget\n\n  // anchors inside an svg have a lowercase nodeName\n  const isAnchorNodeName = nodeName.toUpperCase() === 'A'\n\n  if (\n    (isAnchorNodeName && isModifiedEvent(e)) ||\n    e.currentTarget.hasAttribute('download')\n  ) {\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  if (!isLocalURL(href)) {\n    if (replace) {\n      // browser default behavior does not replace the history state\n      // so we need to do it manually\n      e.preventDefault()\n      location.replace(href)\n    }\n\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  e.preventDefault()\n\n  const navigate = () => {\n    if (onNavigate) {\n      let isDefaultPrevented = false\n\n      onNavigate({\n        preventDefault: () => {\n          isDefaultPrevented = true\n        },\n      })\n\n      if (isDefaultPrevented) {\n        return\n      }\n    }\n\n    dispatchNavigateAction(\n      as || href,\n      replace ? 'replace' : 'push',\n      scroll ?? true,\n      linkInstanceRef.current\n    )\n  }\n\n  React.startTransition(navigate)\n}\n\nfunction formatStringOrUrl(urlObjOrString: UrlObject | string): string {\n  if (typeof urlObjOrString === 'string') {\n    return urlObjOrString\n  }\n\n  return formatUrl(urlObjOrString)\n}\n\n/**\n * A React component that extends the HTML `<a>` element to provide\n * [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation. This is the primary way to navigate between routes in Next.js.\n *\n * @remarks\n * - Prefetching is only enabled in production.\n *\n * @see https://nextjs.org/docs/app/api-reference/components/link\n */\nexport default function LinkComponent(\n  props: LinkProps & {\n    children: React.ReactNode\n    ref: React.Ref<HTMLAnchorElement>\n  }\n) {\n  const [linkStatus, setOptimisticLinkStatus] = useOptimistic(IDLE_LINK_STATUS)\n\n  let children: React.ReactNode\n\n  const linkInstanceRef = useRef<LinkInstance | null>(null)\n\n  const {\n    href: hrefProp,\n    as: asProp,\n    children: childrenProp,\n    prefetch: prefetchProp = null,\n    passHref,\n    replace,\n    shallow,\n    scroll,\n    onClick,\n    onMouseEnter: onMouseEnterProp,\n    onTouchStart: onTouchStartProp,\n    legacyBehavior = false,\n    onNavigate,\n    ref: forwardedRef,\n    unstable_dynamicOnHover,\n    ...restProps\n  } = props\n\n  children = childrenProp\n\n  if (\n    legacyBehavior &&\n    (typeof children === 'string' || typeof children === 'number')\n  ) {\n    children = <a>{children}</a>\n  }\n\n  const router = React.useContext(AppRouterContext)\n\n  const prefetchEnabled = prefetchProp !== false\n  /**\n   * The possible states for prefetch are:\n   * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n   * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n   * - false: we will not prefetch if in the viewport at all\n   * - 'unstable_dynamicOnHover': this starts in \"auto\" mode, but switches to \"full\" when the link is hovered\n   */\n  const appPrefetchKind =\n    prefetchProp === null ? PrefetchKind.AUTO : PrefetchKind.FULL\n\n  if (process.env.NODE_ENV !== 'production') {\n    function createPropError(args: {\n      key: string\n      expected: string\n      actual: string\n    }) {\n      return new Error(\n        `Failed prop type: The prop \\`${args.key}\\` expects a ${args.expected} in \\`<Link>\\`, but got \\`${args.actual}\\` instead.` +\n          (typeof window !== 'undefined'\n            ? \"\\nOpen your browser's console to view the Component stack trace.\"\n            : '')\n      )\n    }\n\n    // TypeScript trick for type-guarding:\n    const requiredPropsGuard: Record<LinkPropsRequired, true> = {\n      href: true,\n    } as const\n    const requiredProps: LinkPropsRequired[] = Object.keys(\n      requiredPropsGuard\n    ) as LinkPropsRequired[]\n    requiredProps.forEach((key: LinkPropsRequired) => {\n      if (key === 'href') {\n        if (\n          props[key] == null ||\n          (typeof props[key] !== 'string' && typeof props[key] !== 'object')\n        ) {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: props[key] === null ? 'null' : typeof props[key],\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const _: never = key\n      }\n    })\n\n    // TypeScript trick for type-guarding:\n    const optionalPropsGuard: Record<LinkPropsOptional, true> = {\n      as: true,\n      replace: true,\n      scroll: true,\n      shallow: true,\n      passHref: true,\n      prefetch: true,\n      unstable_dynamicOnHover: true,\n      onClick: true,\n      onMouseEnter: true,\n      onTouchStart: true,\n      legacyBehavior: true,\n      onNavigate: true,\n    } as const\n    const optionalProps: LinkPropsOptional[] = Object.keys(\n      optionalPropsGuard\n    ) as LinkPropsOptional[]\n    optionalProps.forEach((key: LinkPropsOptional) => {\n      const valType = typeof props[key]\n\n      if (key === 'as') {\n        if (props[key] && valType !== 'string' && valType !== 'object') {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: valType,\n          })\n        }\n      } else if (\n        key === 'onClick' ||\n        key === 'onMouseEnter' ||\n        key === 'onTouchStart' ||\n        key === 'onNavigate'\n      ) {\n        if (props[key] && valType !== 'function') {\n          throw createPropError({\n            key,\n            expected: '`function`',\n            actual: valType,\n          })\n        }\n      } else if (\n        key === 'replace' ||\n        key === 'scroll' ||\n        key === 'shallow' ||\n        key === 'passHref' ||\n        key === 'prefetch' ||\n        key === 'legacyBehavior' ||\n        key === 'unstable_dynamicOnHover'\n      ) {\n        if (props[key] != null && valType !== 'boolean') {\n          throw createPropError({\n            key,\n            expected: '`boolean`',\n            actual: valType,\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const _: never = key\n      }\n    })\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (props.locale) {\n      warnOnce(\n        'The `locale` prop is not supported in `next/link` while using the `app` router. Read more about app router internalization: https://nextjs.org/docs/app/building-your-application/routing/internationalization'\n      )\n    }\n    if (!asProp) {\n      let href: string | undefined\n      if (typeof hrefProp === 'string') {\n        href = hrefProp\n      } else if (\n        typeof hrefProp === 'object' &&\n        typeof hrefProp.pathname === 'string'\n      ) {\n        href = hrefProp.pathname\n      }\n\n      if (href) {\n        const hasDynamicSegment = href\n          .split('/')\n          .some((segment) => segment.startsWith('[') && segment.endsWith(']'))\n\n        if (hasDynamicSegment) {\n          throw new Error(\n            `Dynamic href \\`${href}\\` found in <Link> while using the \\`/app\\` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href`\n          )\n        }\n      }\n    }\n  }\n\n  const { href, as } = React.useMemo(() => {\n    const resolvedHref = formatStringOrUrl(hrefProp)\n    return {\n      href: resolvedHref,\n      as: asProp ? formatStringOrUrl(asProp) : resolvedHref,\n    }\n  }, [hrefProp, asProp])\n\n  // This will return the first child, if multiple are provided it will throw an error\n  let child: any\n  if (legacyBehavior) {\n    if (process.env.NODE_ENV === 'development') {\n      if (onClick) {\n        console.warn(\n          `\"onClick\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link`\n        )\n      }\n      if (onMouseEnterProp) {\n        console.warn(\n          `\"onMouseEnter\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link`\n        )\n      }\n      try {\n        child = React.Children.only(children)\n      } catch (err) {\n        if (!children) {\n          throw new Error(\n            `No children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but one child is required https://nextjs.org/docs/messages/link-no-children`\n          )\n        }\n        throw new Error(\n          `Multiple children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children` +\n            (typeof window !== 'undefined'\n              ? \" \\nOpen your browser's console to view the Component stack trace.\"\n              : '')\n        )\n      }\n    } else {\n      child = React.Children.only(children)\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if ((children as any)?.type === 'a') {\n        throw new Error(\n          'Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'\n        )\n      }\n    }\n  }\n\n  const childRef: any = legacyBehavior\n    ? child && typeof child === 'object' && child.ref\n    : forwardedRef\n\n  // Use a callback ref to attach an IntersectionObserver to the anchor tag on\n  // mount. In the future we will also use this to keep track of all the\n  // currently mounted <Link> instances, e.g. so we can re-prefetch them after\n  // a revalidation or refresh.\n  const observeLinkVisibilityOnMount = React.useCallback(\n    (element: HTMLAnchorElement | SVGAElement) => {\n      if (router !== null) {\n        linkInstanceRef.current = mountLinkInstance(\n          element,\n          href,\n          router,\n          appPrefetchKind,\n          prefetchEnabled,\n          setOptimisticLinkStatus\n        )\n      }\n\n      return () => {\n        if (linkInstanceRef.current) {\n          unmountLinkForCurrentNavigation(linkInstanceRef.current)\n          linkInstanceRef.current = null\n        }\n        unmountPrefetchableInstance(element)\n      }\n    },\n    [prefetchEnabled, href, router, appPrefetchKind, setOptimisticLinkStatus]\n  )\n\n  const mergedRef = useMergedRef(observeLinkVisibilityOnMount, childRef)\n\n  const childProps: {\n    onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n    onMouseEnter: React.MouseEventHandler<HTMLAnchorElement>\n    onClick: React.MouseEventHandler<HTMLAnchorElement>\n    href?: string\n    ref?: any\n  } = {\n    ref: mergedRef,\n    onClick(e) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (!e) {\n          throw new Error(\n            `Component rendered inside next/link has to pass click event to \"onClick\" prop.`\n          )\n        }\n      }\n\n      if (!legacyBehavior && typeof onClick === 'function') {\n        onClick(e)\n      }\n\n      if (\n        legacyBehavior &&\n        child.props &&\n        typeof child.props.onClick === 'function'\n      ) {\n        child.props.onClick(e)\n      }\n\n      if (!router) {\n        return\n      }\n\n      if (e.defaultPrevented) {\n        return\n      }\n\n      linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate)\n    },\n    onMouseEnter(e) {\n      if (!legacyBehavior && typeof onMouseEnterProp === 'function') {\n        onMouseEnterProp(e)\n      }\n\n      if (\n        legacyBehavior &&\n        child.props &&\n        typeof child.props.onMouseEnter === 'function'\n      ) {\n        child.props.onMouseEnter(e)\n      }\n\n      if (!router) {\n        return\n      }\n\n      if (!prefetchEnabled || process.env.NODE_ENV === 'development') {\n        return\n      }\n\n      const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true\n      onNavigationIntent(\n        e.currentTarget as HTMLAnchorElement | SVGAElement,\n        upgradeToDynamicPrefetch\n      )\n    },\n    onTouchStart: process.env.__NEXT_LINK_NO_TOUCH_START\n      ? undefined\n      : function onTouchStart(e) {\n          if (!legacyBehavior && typeof onTouchStartProp === 'function') {\n            onTouchStartProp(e)\n          }\n\n          if (\n            legacyBehavior &&\n            child.props &&\n            typeof child.props.onTouchStart === 'function'\n          ) {\n            child.props.onTouchStart(e)\n          }\n\n          if (!router) {\n            return\n          }\n\n          if (!prefetchEnabled) {\n            return\n          }\n\n          const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true\n          onNavigationIntent(\n            e.currentTarget as HTMLAnchorElement | SVGAElement,\n            upgradeToDynamicPrefetch\n          )\n        },\n  }\n\n  // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n  // defined, we specify the current 'href', so that repetition is not needed by the user.\n  // If the url is absolute, we can bypass the logic to prepend the basePath.\n  if (isAbsoluteUrl(as)) {\n    childProps.href = as\n  } else if (\n    !legacyBehavior ||\n    passHref ||\n    (child.type === 'a' && !('href' in child.props))\n  ) {\n    childProps.href = addBasePath(as)\n  }\n\n  let link: React.ReactNode\n\n  if (legacyBehavior) {\n    if (process.env.NODE_ENV === 'development') {\n      errorOnce(\n        '`legacyBehavior` is deprecated and will be removed in a future ' +\n          'release. A codemod is available to upgrade your components:\\n\\n' +\n          'npx @next/codemod@latest new-link .\\n\\n' +\n          'Learn more: https://nextjs.org/docs/app/building-your-application/upgrading/codemods#remove-a-tags-from-link-components'\n      )\n    }\n    link = React.cloneElement(child, childProps)\n  } else {\n    link = (\n      <a {...restProps} {...childProps}>\n        {children}\n      </a>\n    )\n  }\n\n  return (\n    <LinkStatusContext.Provider value={linkStatus}>\n      {link}\n    </LinkStatusContext.Provider>\n  )\n}\n\nconst LinkStatusContext = createContext<\n  typeof PENDING_LINK_STATUS | typeof IDLE_LINK_STATUS\n>(IDLE_LINK_STATUS)\n\nexport const useLinkStatus = () => {\n  return useContext(LinkStatusContext)\n}\n"], "names": ["LinkComponent", "useLinkStatus", "isModifiedEvent", "event", "eventTarget", "currentTarget", "target", "getAttribute", "metaKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "nativeEvent", "which", "linkClicked", "e", "href", "as", "linkInstanceRef", "replace", "scroll", "onNavigate", "nodeName", "isAnchorNodeName", "toUpperCase", "hasAttribute", "isLocalURL", "preventDefault", "location", "navigate", "isDefaultPrevented", "dispatchNavigateAction", "current", "React", "startTransition", "formatStringOrUrl", "urlObjOrString", "formatUrl", "props", "linkStatus", "setOptimisticLinkStatus", "useOptimistic", "IDLE_LINK_STATUS", "children", "useRef", "hrefProp", "asProp", "childrenProp", "prefetch", "prefetchProp", "passHref", "shallow", "onClick", "onMouseEnter", "onMouseEnterProp", "onTouchStart", "onTouchStartProp", "legacyBeh<PERSON>or", "ref", "forwardedRef", "unstable_dynamicOnHover", "restProps", "a", "router", "useContext", "AppRouterContext", "prefetchEnabled", "appPrefetchKind", "PrefetchKind", "AUTO", "FULL", "process", "env", "NODE_ENV", "createPropError", "args", "Error", "key", "expected", "actual", "window", "requiredPropsGuard", "requiredProps", "Object", "keys", "for<PERSON>ach", "_", "optionalPropsGuard", "optionalProps", "valType", "locale", "warnOnce", "pathname", "hasDynamicSegment", "split", "some", "segment", "startsWith", "endsWith", "useMemo", "resolvedHref", "child", "console", "warn", "Children", "only", "err", "type", "childRef", "observeLinkVisibilityOnMount", "useCallback", "element", "mountLinkInstance", "unmountLinkForCurrentNavigation", "unmountPrefetchableInstance", "mergedRef", "useMergedRef", "childProps", "defaultPrevented", "upgradeToDynamicPrefetch", "onNavigationIntent", "__NEXT_LINK_NO_TOUCH_START", "undefined", "isAbsoluteUrl", "addBasePath", "link", "errorOnce", "cloneElement", "LinkStatusContext", "Provider", "value", "createContext"], "mappings": "AAkXMuE,QAAQC,GAAG,CAACC,QAAQ,KAAK;AAlX/B;;;;;;;;;;;;;;;;IAmTA;;;;;;;;;CASC,GACD,OAyZC,EAAA;eAzZuBzE;;IA+ZXC,aAAa,EAAA;eAAbA;;;;;iEA1tB2D;2BAE9C;+CACO;oCACJ;8BACA;uBACC;6BACF;0BACH;uBASlB;4BACoB;mCACY;2BACb;AA0M1B,SAASC,gBAAgBC,KAAuB;IAC9C,MAAMC,cAAcD,MAAME,aAAa;IACvC,MAAMC,SAASF,YAAYG,YAAY,CAAC;IACxC,OACGD,UAAUA,WAAW,WACtBH,MAAMK,OAAO,IACbL,MAAMM,OAAO,IACbN,MAAMO,QAAQ,IACdP,MAAMQ,MAAM,IAAI,6BAA6B;IAC5CR,MAAMS,WAAW,IAAIT,MAAMS,WAAW,CAACC,KAAK,KAAK;AAEtD;AAEA,SAASC,YACPC,CAAmB,EACnBC,IAAY,EACZC,EAAU,EACVC,eAAqD,EACrDC,OAAiB,EACjBC,MAAgB,EAChBC,UAAmC;IAEnC,MAAM,EAAEC,QAAQ,EAAE,GAAGP,EAAEV,aAAa;IAEpC,kDAAkD;IAClD,MAAMkB,mBAAmBD,SAASE,WAAW,OAAO;IAEpD,IACGD,oBAAoBrB,gBAAgBa,MACrCA,EAAEV,aAAa,CAACoB,YAAY,CAAC,aAC7B;QACA,8CAA8C;QAC9C;IACF;IAEA,IAAI,CAACC,CAAAA,GAAAA,YAAAA,UAAU,EAACV,OAAO;QACrB,IAAIG,SAAS;YACX,8DAA8D;YAC9D,+BAA+B;YAC/BJ,EAAEY,cAAc;YAChBC,SAAST,OAAO,CAACH;QACnB;QAEA,8CAA8C;QAC9C;IACF;IAEAD,EAAEY,cAAc;IAEhB,MAAME,WAAW;QACf,IAAIR,YAAY;YACd,IAAIS,qBAAqB;YAEzBT,WAAW;gBACTM,gBAAgB;oBACdG,qBAAqB;gBACvB;YACF;YAEA,IAAIA,oBAAoB;gBACtB;YACF;QACF;QAEAC,CAAAA,GAAAA,mBAAAA,sBAAsB,EACpBd,MAAMD,MACNG,UAAU,YAAY,QACtBC,UAAAA,OAAAA,SAAU,MACVF,gBAAgBc,OAAO;IAE3B;IAEAC,OAAAA,OAAK,CAACC,eAAe,CAACL;AACxB;AAEA,SAASM,kBAAkBC,cAAkC;IAC3D,IAAI,OAAOA,mBAAmB,UAAU;QACtC,OAAOA;IACT;IAEA,OAAOC,CAAAA,GAAAA,WAAAA,SAAS,EAACD;AACnB;AAYe,SAASpC,cACtBsC,KAGC;IAED,MAAM,CAACC,YAAYC,wBAAwB,GAAGC,CAAAA,GAAAA,OAAAA,aAAa,EAACC,OAAAA,gBAAgB;IAE5E,IAAIC;IAEJ,MAAMzB,kBAAkB0B,CAAAA,GAAAA,OAAAA,MAAM,EAAsB;IAEpD,MAAM,EACJ5B,MAAM6B,QAAQ,EACd5B,IAAI6B,MAAM,EACVH,UAAUI,YAAY,EACtBC,UAAUC,eAAe,IAAI,EAC7BC,QAAQ,EACR/B,OAAO,EACPgC,OAAO,EACP/B,MAAM,EACNgC,OAAO,EACPC,cAAcC,gBAAgB,EAC9BC,cAAcC,gBAAgB,EAC9BC,iBAAiB,KAAK,EACtBpC,UAAU,EACVqC,KAAKC,YAAY,EACjBC,uBAAuB,EACvB,GAAGC,WACJ,GAAGvB;IAEJK,WAAWI;IAEX,IACEU,kBACC,CAAA,OAAOd,aAAa,YAAY,OAAOA,aAAa,QAAO,GAC5D;QACAA,WAAAA,WAAAA,GAAW,CAAA,GAAA,YAAA,GAAA,EAACmB,KAAAA;sBAAGnB;;IACjB;IAEA,MAAMoB,SAAS9B,OAAAA,OAAK,CAAC+B,UAAU,CAACC,+BAAAA,gBAAgB;IAEhD,MAAMC,kBAAkBjB,iBAAiB;IACzC;;;;;;GAMC,GACD,MAAMkB,kBACJlB,iBAAiB,OAAOmB,oBAAAA,YAAY,CAACC,IAAI,GAAGD,oBAAAA,YAAY,CAACE,IAAI;IAE/D,wCAA2C;QACzC,SAASI,gBAAgBC,IAIxB;YACC,OAAO,OAAA,cAKN,CALM,IAAIC,MACR,iCAA+BD,KAAKE,GAAG,GAAC,iBAAeF,KAAKG,QAAQ,GAAC,4BAA4BH,KAAKI,MAAM,GAAC,eAC3G,CAAA,OAAOC,WAAW,cACf,qEACA,EAAC,IAJF,qBAAA;uBAAA;4BAAA;8BAAA;YAKP;QACF;QAEA,sCAAsC;QACtC,MAAMC,qBAAsD;YAC1DjE,MAAM;QACR;QACA,MAAMkE,gBAAqCC,OAAOC,IAAI,CACpDH;QAEFC,cAAcG,OAAO,CAAC,CAACR;YACrB,IAAIA,QAAQ,QAAQ;gBAClB,IACEvC,KAAK,CAACuC,IAAI,IAAI,QACb,OAAOvC,KAAK,CAACuC,IAAI,KAAK,YAAY,OAAOvC,KAAK,CAACuC,IAAI,KAAK,UACzD;oBACA,MAAMH,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQzC,KAAK,CAACuC,IAAI,KAAK,OAAO,SAAS,OAAOvC,KAAK,CAACuC,IAAI;oBAC1D;gBACF;YACF,OAAO;gBACL,sCAAsC;gBACtC,6DAA6D;gBAC7D,MAAMS,IAAWT;YACnB;QACF;QAEA,sCAAsC;QACtC,MAAMU,qBAAsD;YAC1DtE,IAAI;YACJE,SAAS;YACTC,QAAQ;YACR+B,SAAS;YACTD,UAAU;YACVF,UAAU;YACVY,yBAAyB;YACzBR,SAAS;YACTC,cAAc;YACdE,cAAc;YACdE,gBAAgB;YAChBpC,YAAY;QACd;QACA,MAAMmE,gBAAqCL,OAAOC,IAAI,CACpDG;QAEFC,cAAcH,OAAO,CAAC,CAACR;YACrB,MAAMY,UAAU,OAAOnD,KAAK,CAACuC,IAAI;YAEjC,IAAIA,QAAQ,MAAM;gBAChB,IAAIvC,KAAK,CAACuC,IAAI,IAAIY,YAAY,YAAYA,YAAY,UAAU;oBAC9D,MAAMf,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQU;oBACV;gBACF;YACF,OAAO,IACLZ,QAAQ,aACRA,QAAQ,kBACRA,QAAQ,kBACRA,QAAQ,cACR;gBACA,IAAIvC,KAAK,CAACuC,IAAI,IAAIY,YAAY,YAAY;oBACxC,MAAMf,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQU;oBACV;gBACF;YACF,OAAO,IACLZ,QAAQ,aACRA,QAAQ,YACRA,QAAQ,aACRA,QAAQ,cACRA,QAAQ,cACRA,QAAQ,oBACRA,QAAQ,2BACR;gBACA,IAAIvC,KAAK,CAACuC,IAAI,IAAI,QAAQY,YAAY,WAAW;oBAC/C,MAAMf,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQU;oBACV;gBACF;YACF,OAAO;gBACL,sCAAsC;gBACtC,6DAA6D;gBAC7D,MAAMH,IAAWT;YACnB;QACF;IACF;IAEA,IAAIN,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;QACzC,IAAInC,MAAMoD,MAAM,EAAE;YAChBC,CAAAA,GAAAA,UAAAA,QAAQ,EACN;QAEJ;QACA,IAAI,CAAC7C,QAAQ;YACX,IAAI9B;YACJ,IAAI,OAAO6B,aAAa,UAAU;gBAChC7B,OAAO6B;YACT,OAAO,IACL,OAAOA,aAAa,YACpB,OAAOA,SAAS+C,QAAQ,KAAK,UAC7B;gBACA5E,OAAO6B,SAAS+C,QAAQ;YAC1B;YAEA,IAAI5E,MAAM;gBACR,MAAM6E,oBAAoB7E,KACvB8E,KAAK,CAAC,KACNC,IAAI,CAAC,CAACC,UAAYA,QAAQC,UAAU,CAAC,QAAQD,QAAQE,QAAQ,CAAC;gBAEjE,IAAIL,mBAAmB;oBACrB,MAAM,OAAA,cAEL,CAFK,IAAIjB,MACP,mBAAiB5D,OAAK,6IADnB,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;QACF;IACF;IAEA,MAAM,EAAEA,IAAI,EAAEC,EAAE,EAAE,GAAGgB,OAAAA,OAAK,CAACkE,OAAO;iCAAC;YACjC,MAAMC,eAAejE,kBAAkBU;YACvC,OAAO;gBACL7B,MAAMoF;gBACNnF,IAAI6B,SAASX,kBAAkBW,UAAUsD;YAC3C;QACF;gCAAG;QAACvD;QAAUC;KAAO;IAErB,oFAAoF;IACpF,IAAIuD;IACJ,IAAI5C,gBAAgB;QAClB,IAAIc,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;YAC1C,IAAIrB,SAAS;gBACXkD,QAAQC,IAAI,CACT,oDAAoD1D,WAAS;YAElE;YACA,IAAIS,kBAAkB;gBACpBgD,QAAQC,IAAI,CACT,yDAAyD1D,WAAS;YAEvE;YACA,IAAI;gBACFwD,QAAQpE,OAAAA,OAAK,CAACuE,QAAQ,CAACC,IAAI,CAAC9D;YAC9B,EAAE,OAAO+D,KAAK;gBACZ,IAAI,CAAC/D,UAAU;oBACb,MAAM,OAAA,cAEL,CAFK,IAAIiC,MACP,uDAAuD/B,WAAS,kFAD7D,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBACA,MAAM,OAAA,cAKL,CALK,IAAI+B,MACP,6DAA6D/B,WAAS,8FACpE,CAAA,OAAOmC,WAAW,cACf,sEACA,EAAC,IAJH,qBAAA;2BAAA;gCAAA;kCAAA;gBAKN;YACF;QACF,OAAO;;QAEP;IACF,OAAO;QACL,IAAIT,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;YAC1C,IAAI,CAAC9B,YAAAA,OAAAA,KAAAA,IAAAA,SAAkBgE,IAAI,MAAK,KAAK;gBACnC,MAAM,OAAA,cAEL,CAFK,IAAI/B,MACR,oKADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;IACF;IAEA,MAAMgC,WAAgBnD,iBAClB4C,SAAS,OAAOA,UAAU,YAAYA,MAAM3C,GAAG,GAC/CC;IAEJ,4EAA4E;IAC5E,sEAAsE;IACtE,4EAA4E;IAC5E,6BAA6B;IAC7B,MAAMkD,+BAA+B5E,OAAAA,OAAK,CAAC6E,WAAW;mEACpD,CAACC;YACC,IAAIhD,WAAW,MAAM;gBACnB7C,gBAAgBc,OAAO,GAAGgF,CAAAA,GAAAA,OAAAA,iBAAiB,EACzCD,SACA/F,MACA+C,QACAI,iBACAD,iBACA1B;YAEJ;YAEA;2EAAO;oBACL,IAAItB,gBAAgBc,OAAO,EAAE;wBAC3BiF,CAAAA,GAAAA,OAAAA,+BAA+B,EAAC/F,gBAAgBc,OAAO;wBACvDd,gBAAgBc,OAAO,GAAG;oBAC5B;oBACAkF,CAAAA,GAAAA,OAAAA,2BAA2B,EAACH;gBAC9B;;QACF;kEACA;QAAC7C;QAAiBlD;QAAM+C;QAAQI;QAAiB3B;KAAwB;IAG3E,MAAM2E,YAAYC,CAAAA,GAAAA,cAAAA,YAAY,EAACP,8BAA8BD;IAE7D,MAAMS,aAMF;QACF3D,KAAKyD;QACL/D,SAAQrC,CAAC;YACP,IAAIwD,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;gBACzC,IAAI,CAAC1D,GAAG;oBACN,MAAM,OAAA,cAEL,CAFK,IAAI6D,MACP,mFADG,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;YAEA,IAAI,CAACnB,kBAAkB,OAAOL,YAAY,YAAY;gBACpDA,QAAQrC;YACV;YAEA,IACE0C,kBACA4C,MAAM/D,KAAK,IACX,OAAO+D,MAAM/D,KAAK,CAACc,OAAO,KAAK,YAC/B;gBACAiD,MAAM/D,KAAK,CAACc,OAAO,CAACrC;YACtB;YAEA,IAAI,CAACgD,QAAQ;gBACX;YACF;YAEA,IAAIhD,EAAEuG,gBAAgB,EAAE;gBACtB;YACF;YAEAxG,YAAYC,GAAGC,MAAMC,IAAIC,iBAAiBC,SAASC,QAAQC;QAC7D;QACAgC,cAAatC,CAAC;YACZ,IAAI,CAAC0C,kBAAkB,OAAOH,qBAAqB,YAAY;gBAC7DA,iBAAiBvC;YACnB;YAEA,IACE0C,kBACA4C,MAAM/D,KAAK,IACX,OAAO+D,MAAM/D,KAAK,CAACe,YAAY,KAAK,YACpC;gBACAgD,MAAM/D,KAAK,CAACe,YAAY,CAACtC;YAC3B;YAEA,IAAI,CAACgD,QAAQ;gBACX;YACF;YAEA,IAAI,CAACG,mBAAmBK,QAAQC,GAAG,CAACC,IAA4B,IAApB,KAAK;gBAC/C;YACF;;YAEA,MAAM8C,2BAA2B3D,4BAA4B;QAK/D;QACAL,cAAcgB,QAAQC,GAAG,CAACiD,0BAA0B,GAChDC,oCACA,SAASnE,aAAaxC,CAAC;YACrB,IAAI,CAAC0C,kBAAkB,OAAOD,qBAAqB,YAAY;gBAC7DA,iBAAiBzC;YACnB;YAEA,IACE0C,kBACA4C,MAAM/D,KAAK,IACX,OAAO+D,MAAM/D,KAAK,CAACiB,YAAY,KAAK,YACpC;gBACA8C,MAAM/D,KAAK,CAACiB,YAAY,CAACxC;YAC3B;YAEA,IAAI,CAACgD,QAAQ;gBACX;YACF;YAEA,IAAI,CAACG,iBAAiB;gBACpB;YACF;YAEA,MAAMqD,2BAA2B3D,4BAA4B;YAC7D4D,CAAAA,GAAAA,OAAAA,kBAAkB,EAChBzG,EAAEV,aAAa,EACfkH;QAEJ;IACN;IAEA,6FAA6F;IAC7F,wFAAwF;IACxF,2EAA2E;IAC3E,IAAII,CAAAA,GAAAA,OAAAA,aAAa,EAAC1G,KAAK;QACrBoG,WAAWrG,IAAI,GAAGC;IACpB,OAAO,IACL,CAACwC,kBACDP,YACCmD,MAAMM,IAAI,KAAK,OAAO,CAAE,CAAA,UAAUN,MAAM/D,KAAI,GAC7C;QACA+E,WAAWrG,IAAI,GAAG4G,CAAAA,GAAAA,aAAAA,WAAW,EAAC3G;IAChC;IAEA,IAAI4G;IAEJ,IAAIpE,gBAAgB;QAClB,IAAIc,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;YAC1CqD,CAAAA,GAAAA,WAAAA,SAAS,EACP,oEACE,oEACA,4CACA;QAEN;QACAD,OAAAA,WAAAA,GAAO5F,OAAAA,OAAK,CAAC8F,YAAY,CAAC1B,OAAOgB;IACnC,OAAO;QACLQ,OAAAA,WAAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAAC/D,KAAAA;YAAG,GAAGD,SAAS;YAAG,GAAGwD,UAAU;sBAC7B1E;;IAGP;IAEA,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACqF,kBAAkBC,QAAQ,EAAA;QAACC,OAAO3F;kBAChCsF;;AAGP;AAEA,MAAMG,oBAAAA,WAAAA,GAAoBG,CAAAA,GAAAA,OAAAA,aAAa,EAErCzF,OAAAA,gBAAgB;AAEX,MAAMzC,gBAAgB;IAC3B,OAAO+D,CAAAA,GAAAA,OAAAA,UAAU,EAACgE;AACpB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2257, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/ecommerce/E-Commerce-Website/frontend/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}