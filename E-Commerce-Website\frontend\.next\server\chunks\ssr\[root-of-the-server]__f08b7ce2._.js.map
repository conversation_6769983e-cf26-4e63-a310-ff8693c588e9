{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/ecommerce/E-Commerce-Website/frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { createContext, useContext, useState, useEffect } from 'react';\r\nimport Cookies from 'js-cookie';\r\nimport { User } from '@/types';\r\n\r\ninterface AuthContextType {\r\n  user: User | null;\r\n  login: (email: string, password: string, role: 'admin' | 'user') => Promise<boolean>;\r\n  register: (userData: RegisterData) => Promise<boolean>;\r\n  logout: () => void;\r\n  isLoading: boolean;\r\n}\r\n\r\ninterface RegisterData {\r\n  name: string;\r\n  email: string;\r\n  password: string;\r\n  phone?: string;\r\n}\r\n\r\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\r\n\r\nexport const useAuth = () => {\r\n  const context = useContext(AuthContext);\r\n  if (context === undefined) {\r\n    throw new Error('useAuth must be used within an AuthProvider');\r\n  }\r\n  return context;\r\n};\r\n\r\nexport const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\r\n  const [user, setUser] = useState<User | null>(null);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n\r\n  // Check for existing session on mount\r\n  useEffect(() => {\r\n    const token = Cookies.get('auth-token');\r\n    const userData = Cookies.get('user-data');\r\n    \r\n    if (token && userData) {\r\n      try {\r\n        const parsedUser = JSON.parse(userData);\r\n        setUser(parsedUser);\r\n      } catch (error) {\r\n        console.error('Error parsing user data:', error);\r\n        Cookies.remove('auth-token');\r\n        Cookies.remove('user-data');\r\n      }\r\n    }\r\n    setIsLoading(false);\r\n  }, []);\r\n\r\n  const login = async (email: string, password: string, role: 'admin' | 'user'): Promise<boolean> => {\r\n    setIsLoading(true);\r\n    \r\n    try {\r\n      const response = await fetch('/api/auth/login', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({ email, password, role }),\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (response.ok && data.success) {\r\n        const userData: User = {\r\n          id: data.user.id,\r\n          email: data.user.email,\r\n          name: data.user.name,\r\n          role: data.user.role,\r\n          phone: data.user.phone,\r\n          createdAt: new Date(data.user.createdAt),\r\n          updatedAt: new Date(data.user.updatedAt),\r\n        };\r\n\r\n        setUser(userData);\r\n        \r\n        // Store in cookies\r\n        Cookies.set('auth-token', data.token, { expires: 7 }); // 7 days\r\n        Cookies.set('user-data', JSON.stringify(userData), { expires: 7 });\r\n        \r\n        return true;\r\n      } else {\r\n        console.error('Login failed:', data.message);\r\n        return false;\r\n      }\r\n    } catch (error) {\r\n      console.error('Login error:', error);\r\n      return false;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const register = async (userData: RegisterData): Promise<boolean> => {\r\n    setIsLoading(true);\r\n    \r\n    try {\r\n      const response = await fetch('/api/auth/register', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify(userData),\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (response.ok && data.success) {\r\n        // Auto-login after successful registration\r\n        return await login(userData.email, userData.password, 'user');\r\n      } else {\r\n        console.error('Registration failed:', data.message);\r\n        return false;\r\n      }\r\n    } catch (error) {\r\n      console.error('Registration error:', error);\r\n      return false;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const logout = () => {\r\n    setUser(null);\r\n    Cookies.remove('auth-token');\r\n    Cookies.remove('user-data');\r\n    window.location.href = '/';\r\n  };\r\n\r\n  const value = {\r\n    user,\r\n    login,\r\n    register,\r\n    logout,\r\n    isLoading,\r\n  };\r\n\r\n  return (\r\n    <AuthContext.Provider value={value}>\r\n      {children}\r\n    </AuthContext.Provider>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAqBA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,UAAU;IACrB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,MAAM,eAAwD,CAAC,EAAE,QAAQ,EAAE;IAChF,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;QAC1B,MAAM,WAAW,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;QAE7B,IAAI,SAAS,UAAU;YACrB,IAAI;gBACF,MAAM,aAAa,KAAK,KAAK,CAAC;gBAC9B,QAAQ;YACV,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4BAA4B;gBAC1C,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;gBACf,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;YACjB;QACF;QACA,aAAa;IACf,GAAG,EAAE;IAEL,MAAM,QAAQ,OAAO,OAAe,UAAkB;QACpD,aAAa;QAEb,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAO;oBAAU;gBAAK;YAC/C;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,IAAI,KAAK,OAAO,EAAE;gBAC/B,MAAM,WAAiB;oBACrB,IAAI,KAAK,IAAI,CAAC,EAAE;oBAChB,OAAO,KAAK,IAAI,CAAC,KAAK;oBACtB,MAAM,KAAK,IAAI,CAAC,IAAI;oBACpB,MAAM,KAAK,IAAI,CAAC,IAAI;oBACpB,OAAO,KAAK,IAAI,CAAC,KAAK;oBACtB,WAAW,IAAI,KAAK,KAAK,IAAI,CAAC,SAAS;oBACvC,WAAW,IAAI,KAAK,KAAK,IAAI,CAAC,SAAS;gBACzC;gBAEA,QAAQ;gBAER,mBAAmB;gBACnB,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,cAAc,KAAK,KAAK,EAAE;oBAAE,SAAS;gBAAE,IAAI,SAAS;gBAChE,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,aAAa,KAAK,SAAS,CAAC,WAAW;oBAAE,SAAS;gBAAE;gBAEhE,OAAO;YACT,OAAO;gBACL,QAAQ,KAAK,CAAC,iBAAiB,KAAK,OAAO;gBAC3C,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,aAAa;QAEb,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,IAAI,KAAK,OAAO,EAAE;gBAC/B,2CAA2C;gBAC3C,OAAO,MAAM,MAAM,SAAS,KAAK,EAAE,SAAS,QAAQ,EAAE;YACxD,OAAO;gBACL,QAAQ,KAAK,CAAC,wBAAwB,KAAK,OAAO;gBAClD,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,SAAS;QACb,QAAQ;QACR,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACf,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACf,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/ecommerce/E-Commerce-Website/frontend/src/contexts/CartContext.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { createContext, useContext, useState, useEffect } from 'react';\r\nimport Cookies from 'js-cookie';\r\nimport { useAuth } from './AuthContext';\r\n\r\ninterface CartItem {\r\n  id: string;\r\n  productId: string;\r\n  product: any;\r\n  quantity: number;\r\n  total: number;\r\n}\r\n\r\ninterface CartContextType {\r\n  cartItems: CartItem[];\r\n  cartCount: number;\r\n  cartTotal: number;\r\n  addToCart: (productId: string, quantity?: number) => Promise<boolean>;\r\n  removeFromCart: (productId: string) => Promise<boolean>;\r\n  updateQuantity: (productId: string, quantity: number) => Promise<boolean>;\r\n  clearCart: () => void;\r\n  isLoading: boolean;\r\n  refreshCart: () => Promise<void>;\r\n}\r\n\r\nconst CartContext = createContext<CartContextType | undefined>(undefined);\r\n\r\nexport const useCart = () => {\r\n  const context = useContext(CartContext);\r\n  if (context === undefined) {\r\n    throw new Error('useCart must be used within a CartProvider');\r\n  }\r\n  return context;\r\n};\r\n\r\nexport const CartProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\r\n  const { user } = useAuth();\r\n  const [cartItems, setCartItems] = useState<CartItem[]>([]);\r\n  const [cartCount, setCartCount] = useState(0);\r\n  const [cartTotal, setCartTotal] = useState(0);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n\r\n  // Helper function to make authenticated API calls\r\n  const makeAuthenticatedRequest = async (url: string, options: RequestInit = {}) => {\r\n    const token = Cookies.get('auth-token');\r\n    return fetch(url, {\r\n      ...options,\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        'Authorization': `Bearer ${token}`,\r\n        ...options.headers,\r\n      },\r\n    });\r\n  };\r\n\r\n  // Fetch cart data from backend\r\n  const refreshCart = async () => {\r\n    if (!user) {\r\n      setCartItems([]);\r\n      setCartCount(0);\r\n      setCartTotal(0);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setIsLoading(true);\r\n      const response = await makeAuthenticatedRequest('/api/cart');\r\n      const data = await response.json();\r\n\r\n      if (data.success) {\r\n        setCartItems(data.cart.items);\r\n        setCartCount(data.cart.totalItems);\r\n        setCartTotal(data.cart.totalAmount);\r\n      } else {\r\n        console.error('Failed to fetch cart:', data.message);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching cart:', error);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  // Add item to cart\r\n  const addToCart = async (productId: string, quantity: number = 1): Promise<boolean> => {\r\n    if (!user) {\r\n      alert('Please login to add items to cart');\r\n      return false;\r\n    }\r\n\r\n    try {\r\n      setIsLoading(true);\r\n      const response = await makeAuthenticatedRequest('/api/cart', {\r\n        method: 'POST',\r\n        body: JSON.stringify({ productId, quantity }),\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (data.success) {\r\n        await refreshCart();\r\n        return true;\r\n      } else {\r\n        alert(data.message || 'Failed to add item to cart');\r\n        return false;\r\n      }\r\n    } catch (error) {\r\n      console.error('Error adding to cart:', error);\r\n      alert('Failed to add item to cart');\r\n      return false;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  // Remove item from cart\r\n  const removeFromCart = async (productId: string): Promise<boolean> => {\r\n    if (!user) return false;\r\n\r\n    try {\r\n      setIsLoading(true);\r\n      const response = await makeAuthenticatedRequest(`/api/cart?productId=${productId}`, {\r\n        method: 'DELETE',\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (data.success) {\r\n        await refreshCart();\r\n        return true;\r\n      } else {\r\n        alert(data.message || 'Failed to remove item from cart');\r\n        return false;\r\n      }\r\n    } catch (error) {\r\n      console.error('Error removing from cart:', error);\r\n      alert('Failed to remove item from cart');\r\n      return false;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  // Update item quantity\r\n  const updateQuantity = async (productId: string, quantity: number): Promise<boolean> => {\r\n    if (!user) return false;\r\n\r\n    try {\r\n      setIsLoading(true);\r\n      const response = await makeAuthenticatedRequest('/api/cart', {\r\n        method: 'PUT',\r\n        body: JSON.stringify({ productId, quantity }),\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (data.success) {\r\n        await refreshCart();\r\n        return true;\r\n      } else {\r\n        alert(data.message || 'Failed to update cart');\r\n        return false;\r\n      }\r\n    } catch (error) {\r\n      console.error('Error updating cart:', error);\r\n      alert('Failed to update cart');\r\n      return false;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  // Clear cart\r\n  const clearCart = () => {\r\n    setCartItems([]);\r\n    setCartCount(0);\r\n    setCartTotal(0);\r\n  };\r\n\r\n  // Load cart when user changes\r\n  useEffect(() => {\r\n    if (user) {\r\n      refreshCart();\r\n    } else {\r\n      clearCart();\r\n    }\r\n  }, [user]);\r\n\r\n  const value = {\r\n    cartItems,\r\n    cartCount,\r\n    cartTotal,\r\n    addToCart,\r\n    removeFromCart,\r\n    updateQuantity,\r\n    clearCart,\r\n    isLoading,\r\n    refreshCart,\r\n  };\r\n\r\n  return (\r\n    <CartContext.Provider value={value}>\r\n      {children}\r\n    </CartContext.Provider>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AA0BA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,UAAU;IACrB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,MAAM,eAAwD,CAAC,EAAE,QAAQ,EAAE;IAChF,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,kDAAkD;IAClD,MAAM,2BAA2B,OAAO,KAAa,UAAuB,CAAC,CAAC;QAC5E,MAAM,QAAQ,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;QAC1B,OAAO,MAAM,KAAK;YAChB,GAAG,OAAO;YACV,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBAClC,GAAG,QAAQ,OAAO;YACpB;QACF;IACF;IAEA,+BAA+B;IAC/B,MAAM,cAAc;QAClB,IAAI,CAAC,MAAM;YACT,aAAa,EAAE;YACf,aAAa;YACb,aAAa;YACb;QACF;QAEA,IAAI;YACF,aAAa;YACb,MAAM,WAAW,MAAM,yBAAyB;YAChD,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,aAAa,KAAK,IAAI,CAAC,KAAK;gBAC5B,aAAa,KAAK,IAAI,CAAC,UAAU;gBACjC,aAAa,KAAK,IAAI,CAAC,WAAW;YACpC,OAAO;gBACL,QAAQ,KAAK,CAAC,yBAAyB,KAAK,OAAO;YACrD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,aAAa;QACf;IACF;IAEA,mBAAmB;IACnB,MAAM,YAAY,OAAO,WAAmB,WAAmB,CAAC;QAC9D,IAAI,CAAC,MAAM;YACT,MAAM;YACN,OAAO;QACT;QAEA,IAAI;YACF,aAAa;YACb,MAAM,WAAW,MAAM,yBAAyB,aAAa;gBAC3D,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAW;gBAAS;YAC7C;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM;gBACN,OAAO;YACT,OAAO;gBACL,MAAM,KAAK,OAAO,IAAI;gBACtB,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;YACN,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,wBAAwB;IACxB,MAAM,iBAAiB,OAAO;QAC5B,IAAI,CAAC,MAAM,OAAO;QAElB,IAAI;YACF,aAAa;YACb,MAAM,WAAW,MAAM,yBAAyB,CAAC,oBAAoB,EAAE,WAAW,EAAE;gBAClF,QAAQ;YACV;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM;gBACN,OAAO;YACT,OAAO;gBACL,MAAM,KAAK,OAAO,IAAI;gBACtB,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;YACN,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,uBAAuB;IACvB,MAAM,iBAAiB,OAAO,WAAmB;QAC/C,IAAI,CAAC,MAAM,OAAO;QAElB,IAAI;YACF,aAAa;YACb,MAAM,WAAW,MAAM,yBAAyB,aAAa;gBAC3D,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAW;gBAAS;YAC7C;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM;gBACN,OAAO;YACT,OAAO;gBACL,MAAM,KAAK,OAAO,IAAI;gBACtB,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;YACN,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,aAAa;IACb,MAAM,YAAY;QAChB,aAAa,EAAE;QACf,aAAa;QACb,aAAa;IACf;IAEA,8BAA8B;IAC9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR;QACF,OAAO;YACL;QACF;IACF,GAAG;QAAC;KAAK;IAET,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 340, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/ecommerce/E-Commerce-Website/frontend/src/contexts/WishlistContext.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { createContext, useContext, useState, useEffect } from 'react';\r\nimport Cookies from 'js-cookie';\r\nimport { useAuth } from './AuthContext';\r\n\r\ninterface WishlistItem {\r\n  id: string;\r\n  productId: string;\r\n  product: any;\r\n  addedAt: Date;\r\n}\r\n\r\ninterface WishlistContextType {\r\n  wishlistItems: WishlistItem[];\r\n  wishlistCount: number;\r\n  addToWishlist: (productId: string) => Promise<boolean>;\r\n  removeFromWishlist: (productId: string) => Promise<boolean>;\r\n  isInWishlist: (productId: string) => boolean;\r\n  clearWishlist: () => void;\r\n  isLoading: boolean;\r\n  refreshWishlist: () => Promise<void>;\r\n}\r\n\r\nconst WishlistContext = createContext<WishlistContextType | undefined>(undefined);\r\n\r\nexport const useWishlist = () => {\r\n  const context = useContext(WishlistContext);\r\n  if (context === undefined) {\r\n    throw new Error('useWishlist must be used within a WishlistProvider');\r\n  }\r\n  return context;\r\n};\r\n\r\nexport const WishlistProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\r\n  const { user } = useAuth();\r\n  const [wishlistItems, setWishlistItems] = useState<WishlistItem[]>([]);\r\n  const [wishlistCount, setWishlistCount] = useState(0);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n\r\n  // Helper function to make authenticated API calls\r\n  const makeAuthenticatedRequest = async (url: string, options: RequestInit = {}) => {\r\n    const token = Cookies.get('auth-token');\r\n    return fetch(url, {\r\n      ...options,\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        'Authorization': `Bearer ${token}`,\r\n        ...options.headers,\r\n      },\r\n    });\r\n  };\r\n\r\n  // Fetch wishlist data from backend\r\n  const refreshWishlist = async () => {\r\n    if (!user) {\r\n      setWishlistItems([]);\r\n      setWishlistCount(0);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setIsLoading(true);\r\n      const response = await makeAuthenticatedRequest('/api/wishlist');\r\n      const data = await response.json();\r\n\r\n      if (data.success) {\r\n        setWishlistItems(data.wishlist.items);\r\n        setWishlistCount(data.wishlist.totalItems);\r\n      } else {\r\n        console.error('Failed to fetch wishlist:', data.message);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching wishlist:', error);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  // Add item to wishlist\r\n  const addToWishlist = async (productId: string): Promise<boolean> => {\r\n    if (!user) {\r\n      alert('Please login to add items to wishlist');\r\n      return false;\r\n    }\r\n\r\n    try {\r\n      setIsLoading(true);\r\n      const response = await makeAuthenticatedRequest('/api/wishlist', {\r\n        method: 'POST',\r\n        body: JSON.stringify({ productId }),\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (data.success) {\r\n        await refreshWishlist();\r\n        return true;\r\n      } else {\r\n        if (data.message === 'Item already in wishlist') {\r\n          alert('Item is already in your wishlist');\r\n        } else {\r\n          alert(data.message || 'Failed to add item to wishlist');\r\n        }\r\n        return false;\r\n      }\r\n    } catch (error) {\r\n      console.error('Error adding to wishlist:', error);\r\n      alert('Failed to add item to wishlist');\r\n      return false;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  // Remove item from wishlist\r\n  const removeFromWishlist = async (productId: string): Promise<boolean> => {\r\n    if (!user) return false;\r\n\r\n    try {\r\n      setIsLoading(true);\r\n      const response = await makeAuthenticatedRequest(`/api/wishlist?productId=${productId}`, {\r\n        method: 'DELETE',\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (data.success) {\r\n        await refreshWishlist();\r\n        return true;\r\n      } else {\r\n        alert(data.message || 'Failed to remove item from wishlist');\r\n        return false;\r\n      }\r\n    } catch (error) {\r\n      console.error('Error removing from wishlist:', error);\r\n      alert('Failed to remove item from wishlist');\r\n      return false;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  // Check if item is in wishlist\r\n  const isInWishlist = (productId: string): boolean => {\r\n    return wishlistItems.some(item => item.productId === productId);\r\n  };\r\n\r\n  // Clear wishlist\r\n  const clearWishlist = () => {\r\n    setWishlistItems([]);\r\n    setWishlistCount(0);\r\n  };\r\n\r\n  // Load wishlist when user changes\r\n  useEffect(() => {\r\n    if (user) {\r\n      refreshWishlist();\r\n    } else {\r\n      clearWishlist();\r\n    }\r\n  }, [user]);\r\n\r\n  const value = {\r\n    wishlistItems,\r\n    wishlistCount,\r\n    addToWishlist,\r\n    removeFromWishlist,\r\n    isInWishlist,\r\n    clearWishlist,\r\n    isLoading,\r\n    refreshWishlist,\r\n  };\r\n\r\n  return (\r\n    <WishlistContext.Provider value={value}>\r\n      {children}\r\n    </WishlistContext.Provider>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAwBA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAmC;AAEhE,MAAM,cAAc;IACzB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,MAAM,mBAA4D,CAAC,EAAE,QAAQ,EAAE;IACpF,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,kDAAkD;IAClD,MAAM,2BAA2B,OAAO,KAAa,UAAuB,CAAC,CAAC;QAC5E,MAAM,QAAQ,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;QAC1B,OAAO,MAAM,KAAK;YAChB,GAAG,OAAO;YACV,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBAClC,GAAG,QAAQ,OAAO;YACpB;QACF;IACF;IAEA,mCAAmC;IACnC,MAAM,kBAAkB;QACtB,IAAI,CAAC,MAAM;YACT,iBAAiB,EAAE;YACnB,iBAAiB;YACjB;QACF;QAEA,IAAI;YACF,aAAa;YACb,MAAM,WAAW,MAAM,yBAAyB;YAChD,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,iBAAiB,KAAK,QAAQ,CAAC,KAAK;gBACpC,iBAAiB,KAAK,QAAQ,CAAC,UAAU;YAC3C,OAAO;gBACL,QAAQ,KAAK,CAAC,6BAA6B,KAAK,OAAO;YACzD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,aAAa;QACf;IACF;IAEA,uBAAuB;IACvB,MAAM,gBAAgB,OAAO;QAC3B,IAAI,CAAC,MAAM;YACT,MAAM;YACN,OAAO;QACT;QAEA,IAAI;YACF,aAAa;YACb,MAAM,WAAW,MAAM,yBAAyB,iBAAiB;gBAC/D,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAU;YACnC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM;gBACN,OAAO;YACT,OAAO;gBACL,IAAI,KAAK,OAAO,KAAK,4BAA4B;oBAC/C,MAAM;gBACR,OAAO;oBACL,MAAM,KAAK,OAAO,IAAI;gBACxB;gBACA,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;YACN,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,4BAA4B;IAC5B,MAAM,qBAAqB,OAAO;QAChC,IAAI,CAAC,MAAM,OAAO;QAElB,IAAI;YACF,aAAa;YACb,MAAM,WAAW,MAAM,yBAAyB,CAAC,wBAAwB,EAAE,WAAW,EAAE;gBACtF,QAAQ;YACV;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM;gBACN,OAAO;YACT,OAAO;gBACL,MAAM,KAAK,OAAO,IAAI;gBACtB,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;YACN,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,+BAA+B;IAC/B,MAAM,eAAe,CAAC;QACpB,OAAO,cAAc,IAAI,CAAC,CAAA,OAAQ,KAAK,SAAS,KAAK;IACvD;IAEA,iBAAiB;IACjB,MAAM,gBAAgB;QACpB,iBAAiB,EAAE;QACnB,iBAAiB;IACnB;IAEA,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR;QACF,OAAO;YACL;QACF;IACF,GAAG;QAAC;KAAK;IAET,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,gBAAgB,QAAQ;QAAC,OAAO;kBAC9B;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 502, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/ecommerce/E-Commerce-Website/frontend/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 525, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/ecommerce/E-Commerce-Website/frontend/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 532, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/ecommerce/E-Commerce-Website/frontend/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 540, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/ecommerce/E-Commerce-Website/frontend/node_modules/js-cookie/dist/js.cookie.mjs"], "sourcesContent": ["/*! js-cookie v3.0.5 | MIT */\n/* eslint-disable no-var */\nfunction assign (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n    for (var key in source) {\n      target[key] = source[key];\n    }\n  }\n  return target\n}\n/* eslint-enable no-var */\n\n/* eslint-disable no-var */\nvar defaultConverter = {\n  read: function (value) {\n    if (value[0] === '\"') {\n      value = value.slice(1, -1);\n    }\n    return value.replace(/(%[\\dA-F]{2})+/gi, decodeURIComponent)\n  },\n  write: function (value) {\n    return encodeURIComponent(value).replace(\n      /%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,\n      decodeURIComponent\n    )\n  }\n};\n/* eslint-enable no-var */\n\n/* eslint-disable no-var */\n\nfunction init (converter, defaultAttributes) {\n  function set (name, value, attributes) {\n    if (typeof document === 'undefined') {\n      return\n    }\n\n    attributes = assign({}, defaultAttributes, attributes);\n\n    if (typeof attributes.expires === 'number') {\n      attributes.expires = new Date(Date.now() + attributes.expires * 864e5);\n    }\n    if (attributes.expires) {\n      attributes.expires = attributes.expires.toUTCString();\n    }\n\n    name = encodeURIComponent(name)\n      .replace(/%(2[346B]|5E|60|7C)/g, decodeURIComponent)\n      .replace(/[()]/g, escape);\n\n    var stringifiedAttributes = '';\n    for (var attributeName in attributes) {\n      if (!attributes[attributeName]) {\n        continue\n      }\n\n      stringifiedAttributes += '; ' + attributeName;\n\n      if (attributes[attributeName] === true) {\n        continue\n      }\n\n      // Considers RFC 6265 section 5.2:\n      // ...\n      // 3.  If the remaining unparsed-attributes contains a %x3B (\";\")\n      //     character:\n      // Consume the characters of the unparsed-attributes up to,\n      // not including, the first %x3B (\";\") character.\n      // ...\n      stringifiedAttributes += '=' + attributes[attributeName].split(';')[0];\n    }\n\n    return (document.cookie =\n      name + '=' + converter.write(value, name) + stringifiedAttributes)\n  }\n\n  function get (name) {\n    if (typeof document === 'undefined' || (arguments.length && !name)) {\n      return\n    }\n\n    // To prevent the for loop in the first place assign an empty array\n    // in case there are no cookies at all.\n    var cookies = document.cookie ? document.cookie.split('; ') : [];\n    var jar = {};\n    for (var i = 0; i < cookies.length; i++) {\n      var parts = cookies[i].split('=');\n      var value = parts.slice(1).join('=');\n\n      try {\n        var found = decodeURIComponent(parts[0]);\n        jar[found] = converter.read(value, found);\n\n        if (name === found) {\n          break\n        }\n      } catch (e) {}\n    }\n\n    return name ? jar[name] : jar\n  }\n\n  return Object.create(\n    {\n      set,\n      get,\n      remove: function (name, attributes) {\n        set(\n          name,\n          '',\n          assign({}, attributes, {\n            expires: -1\n          })\n        );\n      },\n      withAttributes: function (attributes) {\n        return init(this.converter, assign({}, this.attributes, attributes))\n      },\n      withConverter: function (converter) {\n        return init(assign({}, this.converter, converter), this.attributes)\n      }\n    },\n    {\n      attributes: { value: Object.freeze(defaultAttributes) },\n      converter: { value: Object.freeze(converter) }\n    }\n  )\n}\n\nvar api = init(defaultConverter, { path: '/' });\n/* eslint-enable no-var */\n\nexport { api as default };\n"], "names": [], "mappings": "AAAA,2BAA2B,GAC3B,yBAAyB;;;AACzB,SAAS,OAAQ,MAAM;IACrB,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QACzC,IAAI,SAAS,SAAS,CAAC,EAAE;QACzB,IAAK,IAAI,OAAO,OAAQ;YACtB,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;QAC3B;IACF;IACA,OAAO;AACT;AACA,wBAAwB,GAExB,yBAAyB,GACzB,IAAI,mBAAmB;IACrB,MAAM,SAAU,KAAK;QACnB,IAAI,KAAK,CAAC,EAAE,KAAK,KAAK;YACpB,QAAQ,MAAM,KAAK,CAAC,GAAG,CAAC;QAC1B;QACA,OAAO,MAAM,OAAO,CAAC,oBAAoB;IAC3C;IACA,OAAO,SAAU,KAAK;QACpB,OAAO,mBAAmB,OAAO,OAAO,CACtC,4CACA;IAEJ;AACF;AACA,wBAAwB,GAExB,yBAAyB,GAEzB,SAAS,KAAM,SAAS,EAAE,iBAAiB;IACzC,SAAS,IAAK,IAAI,EAAE,KAAK,EAAE,UAAU;QACnC,IAAI,OAAO,aAAa,aAAa;YACnC;QACF;QAEA,aAAa,OAAO,CAAC,GAAG,mBAAmB;QAE3C,IAAI,OAAO,WAAW,OAAO,KAAK,UAAU;YAC1C,WAAW,OAAO,GAAG,IAAI,KAAK,KAAK,GAAG,KAAK,WAAW,OAAO,GAAG;QAClE;QACA,IAAI,WAAW,OAAO,EAAE;YACtB,WAAW,OAAO,GAAG,WAAW,OAAO,CAAC,WAAW;QACrD;QAEA,OAAO,mBAAmB,MACvB,OAAO,CAAC,wBAAwB,oBAChC,OAAO,CAAC,SAAS;QAEpB,IAAI,wBAAwB;QAC5B,IAAK,IAAI,iBAAiB,WAAY;YACpC,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE;gBAC9B;YACF;YAEA,yBAAyB,OAAO;YAEhC,IAAI,UAAU,CAAC,cAAc,KAAK,MAAM;gBACtC;YACF;YAEA,kCAAkC;YAClC,MAAM;YACN,iEAAiE;YACjE,iBAAiB;YACjB,2DAA2D;YAC3D,iDAAiD;YACjD,MAAM;YACN,yBAAyB,MAAM,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;QACxE;QAEA,OAAQ,SAAS,MAAM,GACrB,OAAO,MAAM,UAAU,KAAK,CAAC,OAAO,QAAQ;IAChD;IAEA,SAAS,IAAK,IAAI;QAChB,IAAI,OAAO,aAAa,eAAgB,UAAU,MAAM,IAAI,CAAC,MAAO;YAClE;QACF;QAEA,mEAAmE;QACnE,uCAAuC;QACvC,IAAI,UAAU,SAAS,MAAM,GAAG,SAAS,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE;QAChE,IAAI,MAAM,CAAC;QACX,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACvC,IAAI,QAAQ,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC;YAC7B,IAAI,QAAQ,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC;YAEhC,IAAI;gBACF,IAAI,QAAQ,mBAAmB,KAAK,CAAC,EAAE;gBACvC,GAAG,CAAC,MAAM,GAAG,UAAU,IAAI,CAAC,OAAO;gBAEnC,IAAI,SAAS,OAAO;oBAClB;gBACF;YACF,EAAE,OAAO,GAAG,CAAC;QACf;QAEA,OAAO,OAAO,GAAG,CAAC,KAAK,GAAG;IAC5B;IAEA,OAAO,OAAO,MAAM,CAClB;QACE;QACA;QACA,QAAQ,SAAU,IAAI,EAAE,UAAU;YAChC,IACE,MACA,IACA,OAAO,CAAC,GAAG,YAAY;gBACrB,SAAS,CAAC;YACZ;QAEJ;QACA,gBAAgB,SAAU,UAAU;YAClC,OAAO,KAAK,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE;QAC1D;QACA,eAAe,SAAU,SAAS;YAChC,OAAO,KAAK,OAAO,CAAC,GAAG,IAAI,CAAC,SAAS,EAAE,YAAY,IAAI,CAAC,UAAU;QACpE;IACF,GACA;QACE,YAAY;YAAE,OAAO,OAAO,MAAM,CAAC;QAAmB;QACtD,WAAW;YAAE,OAAO,OAAO,MAAM,CAAC;QAAW;IAC/C;AAEJ;AAEA,IAAI,MAAM,KAAK,kBAAkB;IAAE,MAAM;AAAI", "ignoreList": [0], "debugId": null}}]}